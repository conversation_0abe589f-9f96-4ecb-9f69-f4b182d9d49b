require('dotenv').config();
const { v2: cloudinary } = require('cloudinary');

// Configure with exact credentials
cloudinary.config({
  cloud_name: 'dyytiuhzh',
  api_key: '585677431321433',
  api_secret: 'LfYKHUNmUyqHXvDZh-p8ILyHpos'
});

async function testCloudinary() {
  try {
    console.log('🧪 Testing Cloudinary with corrected API secret...');
    
    // Test basic ping
    const pingResult = await cloudinary.api.ping();
    console.log('✅ Ping successful:', pingResult);
    
    console.log('🎉 Cloudinary is working correctly!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.error) {
      console.error('📝 Error details:', error.error);
    }
  }
}

testCloudinary();
