const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect, adminPanel, editor } = require('../middleware/auth');
const { adminLimiter, uploadLimiter } = require('../middleware/security');
const upload = require('../middleware/upload');
const Gallery = require('../models/Gallery');
const SystemLog = require('../models/SystemLog');
const { generateUniqueSlug } = require('../utils/slugify');
const { uploadImage, deleteImage } = require('../config/cloudinary');

// Apply middleware to all routes - RATE LIMITING TEMPORARILY DISABLED
// router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/gallery
// @desc    Get all gallery items with pagination and filtering
// @access  Private (Editor/Admin)
router.get('/', editor, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      category = '',
      isActive = '',
      isFeatured = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }
    
    if (category) {
      query.category = category;
    }
    
    if (isActive !== '') {
      query.isActive = isActive === 'true';
    }
    
    if (isFeatured !== '') {
      query.isFeatured = isFeatured === 'true';
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get gallery items
    const galleryItems = await Gallery.find(query)
      .populate('uploadedBy', 'firstName lastName email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Gallery.countDocuments(query);

    res.json({
      success: true,
      data: {
        galleryItems,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error in get gallery items:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/gallery/:id
// @desc    Get gallery item by ID
// @access  Private (Editor/Admin)
router.get('/:id', editor, async (req, res) => {
  try {
    const galleryItem = await Gallery.findById(req.params.id)
      .populate('uploadedBy', 'firstName lastName email');
    
    if (!galleryItem) {
      return res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
    }

    res.json({
      success: true,
      data: { galleryItem }
    });

  } catch (error) {
    console.error('Error in get gallery item by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/gallery
// @desc    Upload new gallery item
// @access  Private (Editor/Admin)
router.post(
  '/',
  editor,
  uploadLimiter,
  upload.single('image'),
  [
    check('title', 'Title is required').not().isEmpty(),
    check('category', 'Category must be valid').isIn(['food', 'recipe', 'nutrition', 'general', 'banner'])
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      console.log('🚀 Starting gallery item creation...');
      console.log('📝 Request body:', req.body);
      console.log('📁 File info:', req.file ? {
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      } : 'No file');

      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Please upload an image'
        });
      }

      const {
        title,
        description = '',
        category = 'general',
        tags = '',
        altText = '',
        isFeatured = false,
        displayOrder = 0
      } = req.body;

      // Generate unique slug
      const slug = await generateUniqueSlug(Gallery, title);

      // Try to upload to Cloudinary, fallback to local storage
      const cloudinaryResult = await uploadImage(req.file.buffer, slug);

      let imageUrl, filename, cloudinaryPublicId = null;

      if (cloudinaryResult.success) {
        // Cloudinary upload successful
        imageUrl = cloudinaryResult.url;
        filename = `${slug}.${cloudinaryResult.format}`;
        cloudinaryPublicId = cloudinaryResult.public_id;
        console.log('✅ Image uploaded to Cloudinary successfully');
      } else {
        // Fallback to local storage
        console.warn('⚠️ Cloudinary upload failed, using local storage fallback:', cloudinaryResult.error);

        // Create uploads directory if it doesn't exist
        const fs = require('fs');
        const path = require('path');
        const uploadsDir = path.join(__dirname, '../public/uploads');
        if (!fs.existsSync(uploadsDir)) {
          fs.mkdirSync(uploadsDir, { recursive: true });
        }

        // Save file locally
        const fileExtension = req.file.originalname.split('.').pop();
        filename = `${slug}.${fileExtension}`;
        const filePath = path.join(uploadsDir, filename);
        fs.writeFileSync(filePath, req.file.buffer);

        imageUrl = `http://localhost:5000/uploads/${filename}`;
        console.log('📁 Image saved locally:', imageUrl);
      }

      // Parse tags
      const tagsArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

      // Create gallery item
      const galleryItem = new Gallery({
        title,
        description,
        imageUrl,
        filename,
        originalName: req.file.originalname,
        mimeType: req.file.mimetype,
        size: cloudinaryResult.success ? cloudinaryResult.bytes : req.file.size,
        slug,
        category,
        tags: tagsArray,
        altText: altText || title,
        isFeatured: isFeatured === 'true',
        displayOrder: parseInt(displayOrder) || 0,
        uploadedBy: req.user._id,
        cloudinaryPublicId,
        metadata: {
          width: cloudinaryResult.success ? cloudinaryResult.width : null,
          height: cloudinaryResult.success ? cloudinaryResult.height : null,
          format: cloudinaryResult.success ? cloudinaryResult.format : filename.split('.').pop()
        }
      });

      await galleryItem.save();

      // Log admin action
      try {
        await SystemLog.logAdmin(
          'gallery_item_created',
          req.user._id,
          req.user.email,
          req.ip,
          {
            galleryItemId: galleryItem._id,
            title: galleryItem.title,
            category: galleryItem.category
          }
        );
      } catch (logError) {
        console.warn('Failed to log admin action:', logError.message);
        // Continue without failing the request
      }

      // Populate uploadedBy for response
      await galleryItem.populate('uploadedBy', 'firstName lastName email');

      res.status(201).json({
        success: true,
        data: { galleryItem }
      });

    } catch (error) {
      console.error('Error in create gallery item:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   PUT /api/admin/gallery/:id
// @desc    Update gallery item
// @access  Private (Editor/Admin)
router.put(
  '/:id',
  editor,
  uploadLimiter,
  upload.single('image'),
  [
    check('title', 'Title cannot be empty').optional().not().isEmpty(),
    check('category', 'Category must be valid').optional().isIn(['food', 'recipe', 'nutrition', 'general', 'banner'])
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      console.log('🔄 Starting gallery item update...');
      console.log('📝 Request body:', req.body);
      console.log('📁 File info:', req.file ? {
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype
      } : 'No file uploaded');

      const {
        title,
        description,
        category,
        tags,
        altText,
        isActive,
        isFeatured,
        displayOrder
      } = req.body;

      // Find gallery item
      const galleryItem = await Gallery.findById(req.params.id);
      if (!galleryItem) {
        return res.status(404).json({
          success: false,
          message: 'Gallery item not found'
        });
      }

      // Store old values for logging
      const oldValues = {
        title: galleryItem.title,
        category: galleryItem.category,
        isActive: galleryItem.isActive,
        isFeatured: galleryItem.isFeatured
      };

      // Handle image upload if provided
      if (req.file) {
        try {
          // Delete old image from Cloudinary if it exists
          if (galleryItem.cloudinaryPublicId) {
            try {
              await deleteImage(galleryItem.cloudinaryPublicId);
            } catch (cloudinaryError) {
              console.warn('Could not delete old image from Cloudinary:', cloudinaryError.message);
            }
          }

          // Generate unique slug for the image
          const imageSlug = `gallery_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

          // Upload new image to Cloudinary
          const result = await uploadImage(req.file.buffer, imageSlug, 'gallery');

          if (result.success) {
            // Update image fields
            galleryItem.imageUrl = result.url;
            galleryItem.cloudinaryPublicId = result.public_id;
            galleryItem.filename = req.file.originalname;
            galleryItem.fileSize = result.bytes;
            galleryItem.mimeType = req.file.mimetype;
          } else {
            throw new Error(result.error || 'Failed to upload image to Cloudinary');
          }
        } catch (uploadError) {
          console.error('Error uploading image to Cloudinary:', uploadError);
          return res.status(500).json({
            success: false,
            message: 'Error uploading image'
          });
        }
      }

      // Update fields
      if (title) {
        galleryItem.title = title;
        // Regenerate slug if title changed
        if (title !== oldValues.title) {
          galleryItem.slug = await generateUniqueSlug(Gallery, title, 'slug', galleryItem._id);
        }
      }
      if (description !== undefined) galleryItem.description = description;
      if (category) galleryItem.category = category;
      if (tags !== undefined) {
        galleryItem.tags = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
      }
      if (altText !== undefined) galleryItem.altText = altText;
      if (isActive !== undefined) galleryItem.isActive = isActive === 'true' || isActive === true;
      if (isFeatured !== undefined) galleryItem.isFeatured = isFeatured === 'true' || isFeatured === true;
      if (displayOrder !== undefined) galleryItem.displayOrder = parseInt(displayOrder) || 0;

      await galleryItem.save();

      // Log admin action
      await SystemLog.logAdmin(
        'gallery_item_updated',
        req.user._id,
        req.user.email,
        req.ip,
        {
          galleryItemId: galleryItem._id,
          oldValues,
          newValues: {
            title: galleryItem.title,
            category: galleryItem.category,
            isActive: galleryItem.isActive,
            isFeatured: galleryItem.isFeatured
          }
        }
      );

      // Populate uploadedBy for response
      await galleryItem.populate('uploadedBy', 'firstName lastName email');

      console.log('✅ Gallery item updated successfully:', galleryItem._id);

      res.json({
        success: true,
        data: { galleryItem }
      });

    } catch (error) {
      console.error('Error in update gallery item:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   DELETE /api/admin/gallery/:id
// @desc    Delete gallery item
// @access  Private (Editor/Admin)
router.delete('/:id', editor, async (req, res) => {
  try {
    const galleryItem = await Gallery.findById(req.params.id);
    
    if (!galleryItem) {
      return res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
    }

    // Store item info for logging before deletion
    const deletedItemInfo = {
      id: galleryItem._id,
      title: galleryItem.title,
      filename: galleryItem.filename,
      cloudinaryPublicId: galleryItem.cloudinaryPublicId
    };

    // Delete from Cloudinary
    if (galleryItem.cloudinaryPublicId) {
      try {
        await deleteImage(galleryItem.cloudinaryPublicId);
      } catch (cloudinaryError) {
        console.warn('Could not delete image from Cloudinary:', cloudinaryError.message);
      }
    }

    // Delete from database
    await Gallery.findByIdAndDelete(req.params.id);

    // Log admin action
    await SystemLog.logAdmin(
      'gallery_item_deleted',
      req.user._id,
      req.user.email,
      req.ip,
      {
        deletedItem: deletedItemInfo
      }
    );

    res.json({
      success: true,
      message: 'Gallery item deleted successfully'
    });

  } catch (error) {
    console.error('Error in delete gallery item:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
