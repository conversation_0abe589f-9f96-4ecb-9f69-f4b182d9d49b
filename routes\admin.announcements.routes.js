const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect, adminPanel, admin, requireRole } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/security');
const Announcement = require('../models/Announcement');
const SystemLog = require('../models/SystemLog');

// Apply middleware to all routes - RATE LIMITING TEMPORARILY DISABLED
// router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/announcements
// @desc    Get all announcements with pagination and filtering (admin view)
// @access  Private (Admin/Editor)
router.get('/', requireRole(['admin', 'editor']), async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      category, 
      importance, 
      status,
      search 
    } = req.query;

    // Build filter query
    const filter = {};
    
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    if (importance && importance !== 'all') {
      filter.importance = importance;
    }
    
    // Status filter (active, expired, scheduled)
    const currentDate = new Date();
    if (status === 'active') {
      filter.publishDate = { $lte: currentDate };
      filter.expiryDate = { $gte: currentDate };
    } else if (status === 'expired') {
      filter.expiryDate = { $lt: currentDate };
    } else if (status === 'scheduled') {
      filter.publishDate = { $gt: currentDate };
    }
    
    // Search filter
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const total = await Announcement.countDocuments(filter);
    const totalPages = Math.ceil(total / limitNum);

    // Find announcements with filters and pagination
    const announcements = await Announcement.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    // Add status to each announcement
    const announcementsWithStatus = announcements.map(announcement => {
      const now = new Date();
      let status = 'scheduled';
      
      if (announcement.publishDate <= now && announcement.expiryDate >= now) {
        status = 'active';
      } else if (announcement.expiryDate < now) {
        status = 'expired';
      }
      
      return {
        ...announcement.toObject(),
        status
      };
    });

    res.json({
      success: true,
      count: announcements.length,
      total,
      announcements: announcementsWithStatus,
      pagination: {
        current: pageNum,
        limit: limitNum,
        total: total,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in get admin announcements:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/announcements/:id
// @desc    Get announcement by ID (admin view)
// @access  Private (Admin/Editor)
router.get('/:id', requireRole(['admin', 'editor']), async (req, res) => {
  try {
    const announcement = await Announcement.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');
    
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    // Add status
    const now = new Date();
    let status = 'scheduled';
    
    if (announcement.publishDate <= now && announcement.expiryDate >= now) {
      status = 'active';
    } else if (announcement.expiryDate < now) {
      status = 'expired';
    }
    
    res.json({
      success: true,
      announcement: {
        ...announcement.toObject(),
        status
      }
    });
  } catch (error) {
    console.error('Error in get announcement by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/announcements
// @desc    Create a new announcement
// @access  Private (Admin/Editor)
router.post(
  '/',
  requireRole(['admin', 'editor']),
  [
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty(),
    check('category', 'Category must be general, maintenance, feature, security, event, or update')
      .isIn(['general', 'maintenance', 'feature', 'security', 'event', 'update']),
    check('importance', 'Importance must be low, medium, or high')
      .isIn(['low', 'medium', 'high']),
    check('publishDate', 'Publish date must be a valid date').optional().isISO8601(),
    check('expiryDate').custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (typeof value === 'string' && new Date(value).toString() !== 'Invalid Date') {
        return true; // Valid date string
      }
      throw new Error('Expiry date must be a valid date or null');
    })
  ],
  async (req, res) => {
    try {
      // Log request body for debugging
      console.log('Create announcement request body:', req.body);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('Validation errors:', errors.array());
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { 
        title, 
        content, 
        category, 
        importance, 
        publishDate, 
        expiryDate 
      } = req.body;
      
      // Create new announcement
      const announcement = new Announcement({
        title,
        content,
        category,
        importance,
        publishDate: publishDate ? new Date(publishDate) : new Date(),
        expiryDate: expiryDate ? new Date(expiryDate) : null,
        createdBy: req.user.id,
        updatedBy: req.user.id
      });
      
      // Save announcement to database
      await announcement.save();
      
      // Populate user fields
      await announcement.populate('createdBy', 'firstName lastName email');
      await announcement.populate('updatedBy', 'firstName lastName email');
      
      // Log the action
      await SystemLog.logAdmin(
        'CREATE_ANNOUNCEMENT',
        req.user.id,
        req.user.email,
        req.ip,
        {
          announcementId: announcement._id,
          title: announcement.title,
          category: announcement.category,
          importance: announcement.importance
        }
      );
      
      res.status(201).json({
        success: true,
        announcement
      });
    } catch (error) {
      console.error('Error in create announcement:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   PUT /api/admin/announcements/:id
// @desc    Update an announcement
// @access  Private (Admin/Editor)
router.put(
  '/:id',
  requireRole(['admin', 'editor']),
  [
    check('title', 'Title is required').optional().not().isEmpty(),
    check('content', 'Content is required').optional().not().isEmpty(),
    check('category', 'Category must be general, maintenance, feature, security, event, or update')
      .optional().isIn(['general', 'maintenance', 'feature', 'security', 'event', 'update']),
    check('importance', 'Importance must be low, medium, or high')
      .optional().isIn(['low', 'medium', 'high']),
    check('publishDate', 'Publish date must be a valid date').optional().isISO8601(),
    check('expiryDate').custom((value) => {
      if (value === null || value === undefined || value === '') {
        return true; // Allow null, undefined, or empty string
      }
      if (typeof value === 'string' && new Date(value).toString() !== 'Invalid Date') {
        return true; // Valid date string
      }
      throw new Error('Expiry date must be a valid date or null');
    })
  ],
  async (req, res) => {
    try {
      // Log request body for debugging
      console.log('Update announcement request body:', req.body);

      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log('Validation errors:', errors.array());
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { 
        title, 
        content, 
        category, 
        importance, 
        publishDate, 
        expiryDate 
      } = req.body;
      
      // Find announcement by ID
      let announcement = await Announcement.findById(req.params.id);
      
      if (!announcement) {
        return res.status(404).json({
          success: false,
          message: 'Announcement not found'
        });
      }
      
      // Store original values for logging
      const originalValues = {
        title: announcement.title,
        content: announcement.content,
        category: announcement.category,
        importance: announcement.importance,
        publishDate: announcement.publishDate,
        expiryDate: announcement.expiryDate
      };
      
      // Update fields
      if (title !== undefined) announcement.title = title;
      if (content !== undefined) announcement.content = content;
      if (category !== undefined) announcement.category = category;
      if (importance !== undefined) announcement.importance = importance;
      if (publishDate !== undefined) announcement.publishDate = new Date(publishDate);
      if (expiryDate !== undefined) announcement.expiryDate = expiryDate ? new Date(expiryDate) : null;
      
      announcement.updatedBy = req.user.id;
      announcement.updatedAt = new Date();
      
      // Save updated announcement
      await announcement.save();
      
      // Populate user fields
      await announcement.populate('createdBy', 'firstName lastName email');
      await announcement.populate('updatedBy', 'firstName lastName email');
      
      // Log the action
      await SystemLog.logAdmin(
        'UPDATE_ANNOUNCEMENT',
        req.user.id,
        req.user.email,
        req.ip,
        {
          announcementId: announcement._id,
          originalValues,
          newValues: {
            title: announcement.title,
            content: announcement.content,
            category: announcement.category,
            importance: announcement.importance,
            publishDate: announcement.publishDate,
            expiryDate: announcement.expiryDate
          }
        }
      );
      
      res.json({
        success: true,
        announcement
      });
    } catch (error) {
      console.error('Error in update announcement:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   DELETE /api/admin/announcements/:id
// @desc    Delete an announcement
// @access  Private (Admin Only)
router.delete('/:id', requireRole(['admin']), async (req, res) => {
  try {
    const { id } = req.params;

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid announcement ID format'
      });
    }

    // Find announcement by ID first to get details for logging
    const announcement = await Announcement.findById(id);

    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }

    // Store announcement details for logging
    const announcementDetails = {
      title: announcement.title,
      category: announcement.category,
      importance: announcement.importance,
      publishDate: announcement.publishDate,
      expiryDate: announcement.expiryDate
    };

    // Delete the announcement
    await Announcement.findByIdAndDelete(id);

    // Log the action
    await SystemLog.logAdmin(
      'DELETE_ANNOUNCEMENT',
      req.user.id,
      req.user.email,
      req.ip,
      {
        announcementId: id,
        deletedAnnouncement: announcementDetails
      }
    );

    res.json({
      success: true,
      message: 'Announcement deleted successfully'
    });
  } catch (error) {
    console.error('Error in delete announcement:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/announcements/stats
// @desc    Get announcement statistics
// @access  Private (Admin/Editor)
router.get('/stats', requireRole(['admin', 'editor']), async (req, res) => {
  try {
    const currentDate = new Date();

    // Get various counts
    const totalAnnouncements = await Announcement.countDocuments();
    const activeAnnouncements = await Announcement.countDocuments({
      publishDate: { $lte: currentDate },
      expiryDate: { $gte: currentDate }
    });
    const expiredAnnouncements = await Announcement.countDocuments({
      expiryDate: { $lt: currentDate }
    });
    const scheduledAnnouncements = await Announcement.countDocuments({
      publishDate: { $gt: currentDate }
    });

    // Get counts by category
    const categoryStats = await Announcement.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get counts by importance
    const importanceStats = await Announcement.aggregate([
      {
        $group: {
          _id: '$importance',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      stats: {
        total: totalAnnouncements,
        active: activeAnnouncements,
        expired: expiredAnnouncements,
        scheduled: scheduledAnnouncements,
        byCategory: categoryStats,
        byImportance: importanceStats
      }
    });
  } catch (error) {
    console.error('Error in get announcement stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
