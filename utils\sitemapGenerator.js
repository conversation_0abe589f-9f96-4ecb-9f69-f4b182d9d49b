/**
 * Sitemap Generation Utilities
 * Provides helper functions for generating dynamic sitemaps
 */

/**
 * Generate sitemap URL entry
 * @param {string} loc - The URL location
 * @param {string} lastmod - Last modification date (ISO string)
 * @param {string} changefreq - Change frequency
 * @param {number} priority - Priority (0.0 to 1.0)
 * @returns {string} - XML URL entry
 */
function generateUrlEntry(loc, lastmod, changefreq = 'monthly', priority = 0.5) {
  return `
  <url>
    <loc>${loc}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
}

/**
 * Generate complete sitemap XML
 * @param {string} baseUrl - Base URL of the site
 * @param {Array} urls - Array of URL objects
 * @returns {string} - Complete sitemap XML
 */
function generateSitemap(baseUrl, urls) {
  let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  urls.forEach(url => {
    sitemap += generateUrlEntry(
      `${baseUrl}${url.path}`,
      url.lastmod,
      url.changefreq,
      url.priority
    );
  });

  sitemap += `
</urlset>`;

  return sitemap;
}

/**
 * Get static pages configuration
 * @returns {Array} - Array of static page configurations
 */
function getStaticPages() {
  const now = new Date().toISOString();
  
  return [
    {
      path: '/',
      lastmod: now,
      changefreq: 'daily',
      priority: 1.0
    },
    {
      path: '/gallery',
      lastmod: now,
      changefreq: 'weekly',
      priority: 0.8
    },
    {
      path: '/announcements',
      lastmod: now,
      changefreq: 'daily',
      priority: 0.8
    },
    {
      path: '/about',
      lastmod: now,
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      path: '/contact',
      lastmod: now,
      changefreq: 'monthly',
      priority: 0.6
    },
    {
      path: '/privacy',
      lastmod: now,
      changefreq: 'yearly',
      priority: 0.3
    },
    {
      path: '/terms',
      lastmod: now,
      changefreq: 'yearly',
      priority: 0.3
    }
  ];
}

/**
 * Convert gallery items to sitemap URLs
 * @param {Array} galleryItems - Gallery items from database
 * @param {string} baseUrl - Base URL
 * @returns {Array} - Array of URL objects
 */
function galleryToSitemapUrls(galleryItems, baseUrl) {
  return galleryItems.map(item => ({
    path: `/gallery/${item.slug}`,
    lastmod: item.updatedAt.toISOString(),
    changefreq: 'monthly',
    priority: item.isFeatured ? 0.8 : 0.7
  }));
}

/**
 * Convert announcements to sitemap URLs
 * @param {Array} announcements - Announcements from database
 * @param {string} baseUrl - Base URL
 * @returns {Array} - Array of URL objects
 */
function announcementsToSitemapUrls(announcements, baseUrl) {
  return announcements.map(announcement => ({
    path: `/announcements/${announcement._id}`,
    lastmod: announcement.updatedAt.toISOString(),
    changefreq: 'weekly',
    priority: announcement.importance === 'high' ? 0.7 : 0.6
  }));
}

/**
 * Generate robots.txt content
 * @param {string} baseUrl - Base URL of the site
 * @param {Object} options - Configuration options
 * @returns {string} - Robots.txt content
 */
function generateRobotsTxt(baseUrl, options = {}) {
  const {
    crawlDelay = 1,
    disallowPaths = ['/api/admin/', '/api/auth/', '/api/users/', '/api/food-analysis/', '/admin/'],
    allowPaths = ['/', '/gallery', '/announcements', '/about', '/contact', '/api/gallery', '/api/announcements', '/api/config-public']
  } = options;

  let robotsTxt = `User-agent: *\n`;
  
  // Add allowed paths
  allowPaths.forEach(path => {
    robotsTxt += `Allow: ${path}\n`;
  });

  robotsTxt += `\n# Disallow admin and private areas\n`;
  
  // Add disallowed paths
  disallowPaths.forEach(path => {
    robotsTxt += `Disallow: ${path}\n`;
  });

  robotsTxt += `\n# Sitemap location\n`;
  robotsTxt += `Sitemap: ${baseUrl}/sitemap.xml\n`;
  robotsTxt += `\n# Crawl delay (optional)\n`;
  robotsTxt += `Crawl-delay: ${crawlDelay}`;

  return robotsTxt;
}

/**
 * Validate sitemap URL
 * @param {string} url - URL to validate
 * @returns {boolean} - Whether URL is valid
 */
function isValidSitemapUrl(url) {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * Calculate sitemap priority based on content type and importance
 * @param {string} type - Content type (page, gallery, announcement, etc.)
 * @param {Object} metadata - Additional metadata
 * @returns {number} - Priority value (0.0 to 1.0)
 */
function calculatePriority(type, metadata = {}) {
  const basePriorities = {
    homepage: 1.0,
    gallery: 0.8,
    announcements: 0.8,
    about: 0.6,
    contact: 0.6,
    galleryItem: 0.7,
    announcement: 0.6,
    static: 0.5
  };

  let priority = basePriorities[type] || 0.5;

  // Adjust based on metadata
  if (metadata.isFeatured) priority += 0.1;
  if (metadata.importance === 'high') priority += 0.1;
  if (metadata.importance === 'low') priority -= 0.1;

  // Ensure priority stays within valid range
  return Math.max(0.0, Math.min(1.0, priority));
}

module.exports = {
  generateUrlEntry,
  generateSitemap,
  getStaticPages,
  galleryToSitemapUrls,
  announcementsToSitemapUrls,
  generateRobotsTxt,
  isValidSitemapUrl,
  calculatePriority
};
