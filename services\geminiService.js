/**
 * Google Gemini API Service
 * Handles food analysis using Google's Gemini 2.0 Flash model
 */

const axios = require('axios');

class GeminiService {
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY;
    this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
    
    if (!this.apiKey) {
      throw new Error('GEMINI_API_KEY is required in environment variables');
    }
  }

  /**
   * Complete system prompt for nutritional analysis (as specified in requirements)
   */
  getSystemPrompt() {
    return `You are a highly specialized nutritional analysis assistant with expertise in food recognition and dietary data estimation. You have access to comprehensive nutritional databases including USDA, international food composition tables, and regional cuisine data. Your primary objective is to analyze a provided image of a meal and produce a comprehensive, accurate nutritional breakdown of each distinct food item.

ACCURACY REQUIREMENTS:
- Use precise nutritional data from established databases (USDA, NCCDB, etc.)
- Consider cooking methods that affect nutritional content (fried vs grilled, etc.)
- Account for regional variations in recipes and ingredients
- Provide realistic portion size estimates based on visual cues
- Include confidence scores that reflect actual uncertainty levels

Task Requirements:
1. Food Identification & Analysis:
- Identify each distinct food item in the image (e.g., "grilled chicken breast," "steamed broccoli," etc.).
- If any item is ambiguous or cannot be confidently identified, label it as "unknown" and include a confidence score (0-100) to reflect uncertainty.

2. Portion Size Estimation:
- Use visual cues like plate size, utensils, and food proportions to estimate realistic portions
- Consider standard serving sizes: rice (150-200g cooked), meat (100-150g), vegetables (80-120g)
- For mixed dishes like biryani, estimate total weight and component breakdown
- Use commonly recognized units (grams, cups, slices, pieces) with metric preference
- Clearly state assumptions: "assuming standard dinner plate (25cm)" or "estimated based on visible portion relative to plate size"

3. Nutritional Data Calculation:
- For each food item, calculate precise nutritional values based on estimated portions:
    - Calories (kcal) - consider cooking methods (oil, butter, etc.)
    - Protein (grams) - account for cooking losses
    - Carbohydrates (grams) - distinguish between simple and complex carbs where relevant
    - Fats (grams) - include added fats from cooking
- Use established nutritional databases (USDA SR, NCCDB) as reference
- For mixed dishes, break down into components: rice + meat + vegetables + oil/spices
- Account for cooking methods: fried foods have 20-30% more calories than grilled
- Consider regional variations: Indian biryani vs Middle Eastern vs Pakistani styles
- If uncertain about specific nutrients, provide ranges (e.g., "25-30g protein") with explanation

4. Aggregate Totals:
- Sum the total calories, protein, carbohydrates, and fats across all items.
- Provide an overall summary of the meal's nutritional content.

5. Output Quality Requirements:
- Return your findings strictly as a well-formatted JSON object using the following structure:
{
    "food_items": [
        {
        "name": "Identified food item name or 'unknown'",
        "portion_size": "Estimated portion size with unit (e.g., '150 grams')",
        "calories": "Estimated calorie count",
        "protein": "Estimated protein content in grams",
        "carbohydrates": "Estimated carbohydrates content in grams",
        "fats": "Estimated fats content in grams",
        "confidence": "Confidence score (0-100) if the identification is not fully certain",
        "notes": "Any relevant observations or assumptions made during analysis (optional)"
        }
        // Repeat for each distinct food item
    ],
    "totals": {
        "total_calories": "Sum of calories for all items",
        "total_protein": "Sum of protein for all items in grams",
        "total_carbohydrates": "Sum of carbohydrates for all items in grams",
        "total_fats": "Sum of fats for all items in grams"
    },
    "overall_notes": "Any additional observations, assumptions, or uncertainties regarding the meal analysis"
}

6. Confidence Scoring Guidelines:
- 95-100%: Clearly identifiable common foods with standard preparations
- 85-94%: Recognizable foods but uncertain about specific preparation or portion
- 70-84%: Food type clear but significant uncertainty about ingredients or cooking method
- 50-69%: Partially obscured or ambiguous foods
- Below 50%: Mark as "unknown" with detailed explanation

7. Warnings & Assumptions:
- If any food item is ambiguous, mark it as "unknown" with an appropriate confidence score.
- Always use standard nutritional data references for your estimates.
- Clearly note any assumptions regarding portion sizes, cooking methods, or ingredient variations.
- For complex dishes, explain component breakdown in notes.
- Ensure that the JSON output is syntactically valid and precise.

8. Image Validation:
- Before performing the analysis, verify if the provided image depicts any food.
- If the image does not contain any food or is not recognized as a food image, return the string "Not a food." and do not proceed with further analysis.

Example JSON Output:
{
    "food_items": [
        {
        "name": "Hamburger",
        "portion_size": "1 burger (approx. 200 grams)",
        "calories": "Approx. 600 kcal",
        "protein": "Approx. 25 g",
        "carbohydrates": "Approx. 45 g",
        "fats": "Approx. 35 g",
        "confidence": "100",
        "notes": "Assuming a typical cheeseburger with a bun, beef patty, cheese, lettuce, and tomato. Values are approximate and could vary depending on specific ingredients and cooking methods."
        },
        {
        "name": "French Fries",
        "portion_size": "1 medium order (approx. 100 grams)",
        "calories": "Approx. 300 kcal",
        "protein": "Approx. 3 g",
        "carbohydrates": "Approx. 40 g",
        "fats": "Approx. 15 g",
        "confidence": "100",
        "notes": "Values are approximate and can vary based on cooking method (e.g., oil used). Assumed standard fast-food variety."
        },
        {
        "name": "Soft Drink",
        "portion_size": "1 medium (approx. 350 ml)",
        "calories": "Approx. 140 kcal",
        "protein": "0 g",
        "carbohydrates": "Approx. 39 g",
        "fats": "0 g",
        "confidence": "100",
        "notes": "Assuming a standard sweetened soft drink. Nutritional values may vary depending on the specific beverage."
        }
    ],
    "totals": {
        "total_calories": "Approx. 1040 kcal",
        "total_protein": "Approx. 28 g",
        "total_carbohydrates": "Approx. 124 g",
        "total_fats": "Approx. 50 g"
    },
    "overall_notes": "Nutritional data is approximate and based on common assumptions and averages. Actual values may vary depending on the specific ingredients used and preparation methods."
}

CRITICAL: ONLY return the JSON object, no additional text or explanations outside the JSON. Ensure the JSON is syntactically valid and precise.`;
  }

  /**
   * Convert image buffer to base64 data URL
   * @param {Buffer} imageBuffer - Image buffer
   * @param {string} mimeType - MIME type of the image
   * @returns {string} - Base64 data URL
   */
  bufferToBase64DataUrl(imageBuffer, mimeType) {
    const base64 = imageBuffer.toString('base64');
    return `data:${mimeType};base64,${base64}`;
  }

  /**
   * Analyze food image using Gemini API
   * @param {Buffer} imageBuffer - Image buffer
   * @param {string} mimeType - MIME type of the image
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeFoodImage(imageBuffer, mimeType) {
    try {
      // Convert image to base64
      const base64Image = imageBuffer.toString('base64');
      
      // Prepare the request payload with complete system prompt
      const requestPayload = {
        contents: [
          {
            parts: [
              {
                text: this.getSystemPrompt() + "\n\nPlease analyze the provided food image and return ONLY the JSON response as specified above. Do not include any additional text, explanations, or formatting outside the JSON structure."
              },
              {
                inline_data: {
                  mime_type: mimeType,
                  data: base64Image
                }
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.05, // Even lower temperature for maximum accuracy
          topK: 20, // Reduced for more focused responses
          topP: 0.8, // Slightly reduced for better consistency
          maxOutputTokens: 4096,
          stopSequences: [], // No stop sequences to ensure complete JSON
        },
        safetySettings: [
          {
            category: "HARM_CATEGORY_HARASSMENT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_HATE_SPEECH",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            threshold: "BLOCK_NONE"
          },
          {
            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
            threshold: "BLOCK_NONE"
          }
        ]
      };

      // Make API call to Gemini
      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        requestPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 30000 // 30 second timeout
        }
      );
      console.log('Full API Response:', response.data);

      // Extract the response text
      const responseText = response.data?.candidates?.[0]?.content?.parts?.[0]?.text;
      console.log('Raw response:', responseText);
      
      if (!responseText) {
        throw new Error('No response text received from Gemini API');
      }

      // Check if it's "Not a food" response
      if (responseText.trim() === "Not a food.") {
        return {
          success: false,
          error: 'NOT_FOOD',
          message: 'The uploaded image does not appear to contain food items.'
        };
      }

      // Try to parse JSON response
      let analysisResult;
      try {
        // Clean the response text (remove any markdown formatting)
        const cleanedText = responseText.replace(/```json\n?|\n?```/g, '').trim();
        analysisResult = JSON.parse(cleanedText);
        console.log('Cleaned JSON:', analysisResult);
      } catch (parseError) {
        console.error('Failed to parse Gemini response as JSON:', parseError);
        console.error('Raw response:', responseText);
        throw new Error('Invalid JSON response from AI analysis');
      }

      // Validate the response structure
      if (!analysisResult.food_items || !analysisResult.totals) {
        throw new Error('Invalid analysis result structure');
      }

      return {
        success: true,
        data: analysisResult,
        raw_response: responseText
      };

    } catch (error) {
      console.error('Gemini API Error:', error);
      
      if (error.response) {
        // API returned an error response
        const status = error.response.status;
        const errorData = error.response.data;
        const message = errorData?.error?.message || 'Unknown API error';

        console.error('Gemini API Error Details:');
        console.error('Status:', status);
        console.error('Error Data:', JSON.stringify(errorData, null, 2));

        return {
          success: false,
          error: 'API_ERROR',
          message: `Gemini API error (${status}): ${message}`,
          details: errorData
        };
      } else if (error.code === 'ECONNABORTED') {
        // Timeout error
        return {
          success: false,
          error: 'TIMEOUT',
          message: 'Analysis request timed out. Please try again.'
        };
      } else {
        // Other errors
        return {
          success: false,
          error: 'UNKNOWN_ERROR',
          message: error.message || 'An unexpected error occurred during analysis.'
        };
      }
    }
  }

  /**
   * Test the Gemini API connection
   * @returns {Promise<Object>} - Test result
   */
  async testConnection() {
    try {
      const testPayload = {
        contents: [
          {
            parts: [
              {
                text: "Hello, please respond with 'API connection successful' if you can read this message."
              }
            ]
          }
        ]
      };

      const response = await axios.post(
        `${this.baseUrl}?key=${this.apiKey}`,
        testPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      const responseText = response.data?.candidates?.[0]?.content?.parts?.[0]?.text;
      
      return {
        success: true,
        message: 'Gemini API connection successful',
        response: responseText
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        details: error.response?.data || null
      };
    }
  }
}

module.exports = GeminiService;
