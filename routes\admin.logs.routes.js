const express = require('express');
const router = express.Router();
const { protect, adminPanel, admin } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/security');
const SystemLog = require('../models/SystemLog');

// Apply middleware to all routes
router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/logs
// @desc    Get system logs with pagination and filtering
// @access  Private (Admin Only)
router.get('/', admin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      level = '',
      category = '',
      userId = '',
      startDate = '',
      endDate = '',
      search = ''
    } = req.query;

    // Build query
    const query = {};
    
    if (level) {
      query.level = level;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (userId) {
      query.userId = userId;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    }
    
    if (search) {
      query.$or = [
        { message: { $regex: search, $options: 'i' } },
        { action: { $regex: search, $options: 'i' } },
        { userEmail: { $regex: search, $options: 'i' } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get logs
    const logs = await SystemLog.find(query)
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await SystemLog.countDocuments(query);

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error in get system logs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/logs/stats
// @desc    Get log statistics
// @access  Private (Admin Only)
router.get('/stats', admin, async (req, res) => {
  try {
    const { days = 7 } = req.query;
    
    // Get log statistics
    const stats = await SystemLog.getLogStats(parseInt(days));
    
    // Get recent error count
    const recentErrors = await SystemLog.countDocuments({
      level: 'error',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    // Get recent critical count
    const recentCritical = await SystemLog.countDocuments({
      level: 'critical',
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });
    
    // Get total logs by level
    const levelStats = await SystemLog.aggregate([
      {
        $group: {
          _id: '$level',
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Get total logs by category
    const categoryStats = await SystemLog.aggregate([
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 }
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        detailedStats: stats,
        summary: {
          recentErrors,
          recentCritical,
          levelBreakdown: levelStats,
          categoryBreakdown: categoryStats
        }
      }
    });

  } catch (error) {
    console.error('Error in get log stats:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/logs/recent
// @desc    Get recent logs (last 100)
// @access  Private (Admin Only)
router.get('/recent', admin, async (req, res) => {
  try {
    const { limit = 100, level = '', category = '' } = req.query;
    
    const recentLogs = await SystemLog.getRecentLogs(
      parseInt(limit),
      level || null,
      category || null
    );

    res.json({
      success: true,
      data: {
        logs: recentLogs,
        count: recentLogs.length
      }
    });

  } catch (error) {
    console.error('Error in get recent logs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/logs/export
// @desc    Export logs as JSON/CSV
// @access  Private (Admin Only)
router.get('/export', admin, async (req, res) => {
  try {
    const { format = 'json' } = req.query;

    // Get logs (limit to 1000 for performance)
    const logs = await SystemLog.find({})
      .sort({ createdAt: -1 })
      .limit(1000);

    if (format === 'csv') {
      // Convert to CSV format
      const csvHeader = 'Date,Level,Category,Action,Message\n';
      const csvData = logs.map(log => {
        const date = log.createdAt ? log.createdAt.toISOString() : '';
        const message = (log.message || '').replace(/"/g, '""');
        return `"${date}","${log.level || ''}","${log.category || ''}","${log.action || ''}","${message}"`;
      }).join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="system-logs.csv"');
      res.send(csvHeader + csvData);
    } else {
      // Return as JSON
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename="system-logs.json"');
      res.json({
        success: true,
        exportDate: new Date().toISOString(),
        totalRecords: logs.length,
        data: logs
      });
    }

  } catch (error) {
    console.error('Error in export logs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/logs/:id
// @desc    Get specific log by ID
// @access  Private (Admin Only)
router.get('/:id', admin, async (req, res) => {
  try {
    const log = await SystemLog.findById(req.params.id)
      .populate('userId', 'firstName lastName email');
    
    if (!log) {
      return res.status(404).json({
        success: false,
        message: 'Log entry not found'
      });
    }

    res.json({
      success: true,
      data: { log }
    });

  } catch (error) {
    console.error('Error in get log by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/admin/logs/cleanup
// @desc    Clean up old logs
// @access  Private (Admin Only)
router.delete('/cleanup', admin, async (req, res) => {
  try {
    const { days = 30, level = '' } = req.body;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));
    
    const query = {
      createdAt: { $lt: cutoffDate }
    };
    
    if (level) {
      query.level = level;
    }
    
    const result = await SystemLog.deleteMany(query);
    
    // Log the cleanup action
    await SystemLog.logAdmin(
      'logs_cleanup',
      req.user._id,
      req.user.email,
      req.ip,
      {
        deletedCount: result.deletedCount,
        cutoffDate,
        level: level || 'all'
      }
    );

    res.json({
      success: true,
      message: `Cleaned up ${result.deletedCount} log entries`,
      data: {
        deletedCount: result.deletedCount,
        cutoffDate
      }
    });

  } catch (error) {
    console.error('Error in cleanup logs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
