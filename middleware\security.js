const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const session = require('express-session');
const { v4: uuidv4 } = require('uuid');

// Rate limiting configurations
const createRateLimit = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
};

// General API rate limit
const apiLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  process.env.NODE_ENV === 'development' ? 1000 : 100, // Higher limit for development
  'Too many requests from this IP, please try again later.'
);

// Strict rate limit for authentication endpoints
const authLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  process.env.NODE_ENV === 'development' ? 50 : 5, // Higher limit for development
  'Too many authentication attempts, please try again later.'
);

// Admin panel rate limit
const adminLimiter = createRateLimit(
  15 * 60 * 1000, // 15 minutes
  process.env.NODE_ENV === 'development' ? 500 : 50, // Higher limit for development
  'Too many admin requests, please try again later.'
);

// Upload rate limit
const uploadLimiter = createRateLimit(
  60 * 60 * 1000, // 1 hour
  20, // limit each IP to 20 uploads per hour
  'Too many upload attempts, please try again later.'
);

// Helmet configuration for security headers
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
});

// Session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'your-session-secret-change-this',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  },
  name: 'nutrisnap.sid',
  genid: () => uuidv4()
};

// CSRF token generation (simple implementation since csurf is deprecated)
const generateCSRFToken = () => {
  return uuidv4();
};

// CSRF protection middleware
const csrfProtection = (req, res, next) => {
  // Skip CSRF for GET requests and API endpoints that don't modify data
  if (req.method === 'GET' || req.path.startsWith('/api/auth/login') || req.path.startsWith('/api/auth/register')) {
    return next();
  }

  // Generate CSRF token for session if not exists
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateCSRFToken();
  }

  // Check CSRF token for state-changing requests
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(req.method)) {
    const token = req.headers['x-csrf-token'] || req.body._csrf;
    
    if (!token || token !== req.session.csrfToken) {
      return res.status(403).json({
        success: false,
        message: 'Invalid CSRF token'
      });
    }
  }

  next();
};

// Middleware to add CSRF token to response
const addCSRFToken = (req, res, next) => {
  if (!req.session.csrfToken) {
    req.session.csrfToken = generateCSRFToken();
  }
  
  res.locals.csrfToken = req.session.csrfToken;
  next();
};

// IP whitelist middleware for admin panel
const adminIPWhitelist = (req, res, next) => {
  // Skip in development
  if (process.env.NODE_ENV === 'development') {
    return next();
  }

  const allowedIPs = process.env.ADMIN_ALLOWED_IPS ? 
    process.env.ADMIN_ALLOWED_IPS.split(',') : [];

  if (allowedIPs.length === 0) {
    return next(); // No IP restriction if not configured
  }

  const clientIP = req.ip || req.connection.remoteAddress;
  
  if (!allowedIPs.includes(clientIP)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied: IP not allowed'
    });
  }

  next();
};

// Security headers middleware
const securityHeaders = (req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  next();
};

// Track failed login attempts per IP
const failedAttempts = new Map();

// Clean up old failed attempts every 15 minutes
setInterval(() => {
  const now = Date.now();
  for (const [ip, data] of failedAttempts.entries()) {
    if (now - data.lastAttempt > 15 * 60 * 1000) { // 15 minutes
      failedAttempts.delete(ip);
    }
  }
}, 15 * 60 * 1000);

// Security monitoring middleware
const securityMonitor = async (req, res, next) => {
  const startTime = Date.now();
  const ip = req.ip;

  res.on('finish', async () => {
    const duration = Date.now() - startTime;

    // Monitor failed login attempts
    if (req.path === '/api/auth/login' && res.statusCode === 401) {
      const attempts = failedAttempts.get(ip) || { count: 0, lastAttempt: 0 };
      attempts.count++;
      attempts.lastAttempt = Date.now();
      failedAttempts.set(ip, attempts);

      // Log warning for multiple failed attempts
      if (attempts.count >= 3) {
        const SystemLog = require('../models/SystemLog');
        await SystemLog.logEvent({
          level: 'warning',
          category: 'security',
          action: 'suspicious_activity_detected',
          message: 'Multiple failed login attempts detected from same IP address',
          userId: null,
          userEmail: '<EMAIL>',
          ipAddress: ip,
          userAgent: req.get('User-Agent'),
          endpoint: req.originalUrl,
          method: req.method,
          metadata: {
            failedAttempts: attempts.count,
            timeWindow: '15 minutes',
            suspiciousIP: ip,
            action: attempts.count >= 5 ? 'temporary_block' : 'monitoring',
            timestamp: new Date().toISOString()
          }
        });
      }
    }

    // Clear failed attempts on successful login
    if (req.path === '/api/auth/login' && res.statusCode === 200) {
      failedAttempts.delete(ip);
    }

    // Log other suspicious activities
    if (res.statusCode >= 500) {
      const SystemLog = require('../models/SystemLog');
      await SystemLog.logEvent({
        level: 'error',
        category: 'system',
        action: 'server_error',
        message: `Server error occurred: ${res.statusCode}`,
        userId: req.user ? req.user._id : null,
        userEmail: req.user ? req.user.email : 'unknown',
        ipAddress: ip,
        userAgent: req.get('User-Agent'),
        endpoint: req.originalUrl,
        method: req.method,
        statusCode: res.statusCode,
        responseTime: duration,
        metadata: {
          duration,
          timestamp: new Date().toISOString()
        }
      });
    }
  });

  next();
};

// Request logging middleware for security monitoring
const securityLogger = (req, res, next) => {
  const startTime = Date.now();

  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      statusCode: res.statusCode,
      duration,
      timestamp: new Date().toISOString()
    };

    // Log suspicious activities
    if (res.statusCode >= 400 || duration > 5000) {
      console.log('Security Log:', JSON.stringify(logData));
    }
  });

  next();
};

module.exports = {
  helmetConfig,
  sessionConfig,
  apiLimiter,
  authLimiter,
  adminLimiter,
  uploadLimiter,
  csrfProtection,
  addCSRFToken,
  adminIPWhitelist,
  securityHeaders,
  securityLogger,
  securityMonitor,
  generateCSRFToken
};
