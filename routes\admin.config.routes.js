const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect, adminPanel, admin } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/security');
const SystemConfig = require('../models/SystemConfig');
const SystemLog = require('../models/SystemLog');

// @route   GET /api/admin/config/public
// @desc    Get public system configurations (for frontend)
// @access  Public
router.get('/public', async (req, res) => {
  try {
    const publicConfigs = await SystemConfig.getPublicConfigs();

    res.json({
      success: true,
      data: publicConfigs
    });

  } catch (error) {
    console.error('Error in get public configs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Apply middleware to all other routes - RATE LIMITING TEMPORARILY DISABLED
// router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/config
// @desc    Get all system configurations
// @access  Private (Admin Only)
router.get('/', admin, async (req, res) => {
  try {
    const { category = '', search = '' } = req.query;

    // Build query
    const query = {};
    
    if (category) {
      query.category = category;
    }
    
    if (search) {
      query.$or = [
        { key: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ];
    }

    // Get configurations
    const configs = await SystemConfig.find(query)
      .populate('updatedBy', 'firstName lastName email')
      .sort({ category: 1, key: 1 });

    // Group by category
    const groupedConfigs = configs.reduce((acc, config) => {
      if (!acc[config.category]) {
        acc[config.category] = [];
      }
      acc[config.category].push(config);
      return acc;
    }, {});

    res.json({
      success: true,
      data: {
        configs: groupedConfigs,
        total: configs.length
      }
    });

  } catch (error) {
    console.error('Error in get system configs:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/config/:key
// @desc    Get specific configuration by key
// @access  Private (Admin Only)
router.get('/:key', admin, async (req, res) => {
  try {
    const config = await SystemConfig.findOne({ key: req.params.key })
      .populate('updatedBy', 'firstName lastName email');
    
    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'Configuration not found'
      });
    }

    res.json({
      success: true,
      data: { config }
    });

  } catch (error) {
    console.error('Error in get config by key:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/config
// @desc    Create or update system configuration
// @access  Private (Admin Only)
router.post(
  '/',
  admin,
  [
    check('key', 'Configuration key is required').not().isEmpty(),
    check('value', 'Configuration value is required').exists(),
    check('type', 'Type must be string, number, boolean, object, or array')
      .isIn(['string', 'number', 'boolean', 'object', 'array']),
    check('category', 'Category must be valid')
      .isIn(['general', 'appearance', 'security', 'email', 'api', 'features'])
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      const {
        key,
        value,
        type,
        category = 'general',
        description = '',
        isPublic = false,
        isEditable = true
      } = req.body;

      // Validate value type
      let processedValue = value;
      switch (type) {
        case 'number':
          processedValue = Number(value);
          if (isNaN(processedValue)) {
            return res.status(400).json({
              success: false,
              message: 'Value must be a valid number'
            });
          }
          break;
        case 'boolean':
          processedValue = Boolean(value);
          break;
        case 'object':
        case 'array':
          if (typeof value === 'string') {
            try {
              processedValue = JSON.parse(value);
            } catch (e) {
              return res.status(400).json({
                success: false,
                message: 'Value must be valid JSON for object/array type'
              });
            }
          }
          break;
      }

      // Check if config exists
      const existingConfig = await SystemConfig.findOne({ key });
      const isUpdate = !!existingConfig;

      // Create or update configuration
      const config = await SystemConfig.setConfig(key, processedValue, type, req.user._id);

      // Update additional fields if creating new config
      if (!isUpdate) {
        config.category = category;
        config.description = description;
        config.isPublic = isPublic;
        config.isEditable = isEditable;
        await config.save();
      }

      // Log admin action
      await SystemLog.logAdmin(
        isUpdate ? 'config_updated' : 'config_created',
        req.user._id,
        req.user.email,
        req.ip,
        {
          configKey: key,
          configValue: processedValue,
          configType: type,
          isUpdate
        }
      );

      // Populate updatedBy for response
      await config.populate('updatedBy', 'firstName lastName email');

      res.status(isUpdate ? 200 : 201).json({
        success: true,
        data: { config }
      });

    } catch (error) {
      console.error('Error in create/update config:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   PUT /api/admin/config/:key
// @desc    Update specific configuration
// @access  Private (Admin Only)
router.put(
  '/:key',
  admin,
  [
    check('value', 'Configuration value is required').exists()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      const { value, description, isPublic, isEditable } = req.body;

      // Find existing configuration
      const config = await SystemConfig.findOne({ key: req.params.key });
      
      if (!config) {
        return res.status(404).json({
          success: false,
          message: 'Configuration not found'
        });
      }

      if (!config.isEditable) {
        return res.status(400).json({
          success: false,
          message: 'This configuration is not editable'
        });
      }

      // Store old value for logging
      const oldValue = config.value;

      // Validate and process value based on type
      let processedValue = value;
      switch (config.type) {
        case 'number':
          processedValue = Number(value);
          if (isNaN(processedValue)) {
            return res.status(400).json({
              success: false,
              message: 'Value must be a valid number'
            });
          }
          break;
        case 'boolean':
          processedValue = Boolean(value);
          break;
        case 'object':
        case 'array':
          if (typeof value === 'string') {
            try {
              processedValue = JSON.parse(value);
            } catch (e) {
              return res.status(400).json({
                success: false,
                message: 'Value must be valid JSON for object/array type'
              });
            }
          }
          break;
      }

      // Update configuration
      config.value = processedValue;
      if (description !== undefined) config.description = description;
      if (typeof isPublic === 'boolean') config.isPublic = isPublic;
      if (typeof isEditable === 'boolean') config.isEditable = isEditable;
      config.updatedBy = req.user._id;
      config.updatedAt = new Date();

      await config.save();

      // Log admin action
      await SystemLog.logAdmin(
        'config_updated',
        req.user._id,
        req.user.email,
        req.ip,
        {
          configKey: config.key,
          oldValue,
          newValue: processedValue
        }
      );

      // Populate updatedBy for response
      await config.populate('updatedBy', 'firstName lastName email');

      res.json({
        success: true,
        data: { config }
      });

    } catch (error) {
      console.error('Error in update config:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   DELETE /api/admin/config/:key
// @desc    Delete system configuration
// @access  Private (Admin Only)
router.delete('/:key', admin, async (req, res) => {
  try {
    const config = await SystemConfig.findOne({ key: req.params.key });
    
    if (!config) {
      return res.status(404).json({
        success: false,
        message: 'Configuration not found'
      });
    }

    if (!config.isEditable) {
      return res.status(400).json({
        success: false,
        message: 'This configuration cannot be deleted'
      });
    }

    // Store config info for logging before deletion
    const deletedConfigInfo = {
      key: config.key,
      value: config.value,
      type: config.type,
      category: config.category
    };

    await SystemConfig.findOneAndDelete({ key: req.params.key });

    // Log admin action
    await SystemLog.logAdmin(
      'config_deleted',
      req.user._id,
      req.user.email,
      req.ip,
      {
        deletedConfig: deletedConfigInfo
      }
    );

    res.json({
      success: true,
      message: 'Configuration deleted successfully'
    });

  } catch (error) {
    console.error('Error in delete config:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
