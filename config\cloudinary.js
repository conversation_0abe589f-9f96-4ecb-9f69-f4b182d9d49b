const { v2: cloudinary } = require('cloudinary');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dyytiuhzh',
  api_key: process.env.CLOUDINARY_API_KEY || '585677431321433',
  api_secret: process.env.CLOUDINARY_API_SECRET
});

// Validate configuration
const validateConfig = () => {
  const config = cloudinary.config();
  if (!config.cloud_name || !config.api_key || !config.api_secret) {
    console.warn('⚠️  Cloudinary configuration incomplete. Please set CLOUDINARY_API_SECRET in your .env file.');
    return false;
  }
  return true;
};

// Check configuration on module load
validateConfig();

/**
 * Upload image to Cloudinary with slug-based public_id using upload_stream
 * @param {Buffer} fileBuffer - Image file buffer
 * @param {string} slug - Slug to use as public_id
 * @param {string} folder - Cloudinary folder (optional)
 * @returns {Promise<Object>} Cloudinary upload result
 */
const uploadImage = async (fileBuffer, slug, folder = 'nutrisnap') => {
  // Check if Cloudinary is properly configured
  if (!validateConfig()) {
    return {
      success: false,
      error: 'Cloudinary is not properly configured. Please check your API credentials.'
    };
  }

  return new Promise((resolve) => {
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        public_id: `${folder}/${slug}`,
        overwrite: true,
        resource_type: 'image',
        transformation: [
          { fetch_format: 'auto', quality: 'auto' }
        ]
      },
      (error, result) => {
        if (error) {
          console.error('Cloudinary upload error:', error);
          resolve({
            success: false,
            error: error.message
          });
        } else {
          resolve({
            success: true,
            url: result.secure_url,
            public_id: result.public_id,
            format: result.format,
            width: result.width,
            height: result.height,
            bytes: result.bytes
          });
        }
      }
    );

    uploadStream.end(fileBuffer);
  });
};

/**
 * Delete image from Cloudinary
 * @param {string} public_id - Cloudinary public_id
 * @returns {Promise<Object>} Deletion result
 */
const deleteImage = async (public_id) => {
  try {
    const result = await cloudinary.uploader.destroy(public_id);
    return {
      success: result.result === 'ok',
      result: result.result
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

/**
 * Generate optimized image URL
 * @param {string} public_id - Cloudinary public_id
 * @param {Object} options - Transformation options
 * @returns {string} Optimized image URL
 */
const getOptimizedUrl = (public_id, options = {}) => {
  const defaultOptions = {
    fetch_format: 'auto',
    quality: 'auto',
    ...options
  };
  
  return cloudinary.url(public_id, defaultOptions);
};

/**
 * Generate thumbnail URL
 * @param {string} public_id - Cloudinary public_id
 * @param {number} width - Thumbnail width
 * @param {number} height - Thumbnail height
 * @returns {string} Thumbnail URL
 */
const getThumbnailUrl = (public_id, width = 300, height = 300) => {
  return cloudinary.url(public_id, {
    crop: 'fill',
    gravity: 'auto',
    width,
    height,
    fetch_format: 'auto',
    quality: 'auto'
  });
};

module.exports = {
  cloudinary,
  uploadImage,
  deleteImage,
  getOptimizedUrl,
  getThumbnailUrl
};
