<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Logs - NutriSnap</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-card h3 {
            color: #7f8c8d;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-card.total .stat-number { color: #3498db; }
        .stat-card.errors .stat-number { color: #e74c3c; }
        .stat-card.warnings .stat-number { color: #f39c12; }
        .stat-card.info .stat-number { color: #27ae60; }

        .logs-section {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: #34495e;
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
        }

        .filters {
            padding: 20px;
            border-bottom: 1px solid #ecf0f1;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .filter-group label {
            font-size: 12px;
            color: #7f8c8d;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .filter-group select, .filter-group input {
            padding: 8px 12px;
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            font-size: 14px;
        }

        .logs-table {
            width: 100%;
            border-collapse: collapse;
        }

        .logs-table th,
        .logs-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .logs-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .log-level {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .log-level.info { background: #d5f4e6; color: #27ae60; }
        .log-level.warning { background: #fef9e7; color: #f39c12; }
        .log-level.error { background: #fadbd8; color: #e74c3c; }
        .log-level.debug { background: #eaf2f8; color: #3498db; }
        .log-level.critical { background: #f4cccc; color: #c0392b; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .error-message {
            background: #fadbd8;
            color: #e74c3c;
            padding: 15px;
            border-radius: 4px;
            margin: 20px;
        }

        .pagination {
            padding: 20px;
            text-align: center;
            border-top: 1px solid #ecf0f1;
        }

        .pagination button {
            padding: 8px 16px;
            margin: 0 5px;
            border: 1px solid #bdc3c7;
            background: white;
            border-radius: 4px;
            cursor: pointer;
        }

        .pagination button:hover {
            background: #ecf0f1;
        }

        .pagination button.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>System Logs</h1>
            <p>Monitor and analyze system activity and events</p>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stat-card total">
                <h3>Total Logs</h3>
                <div class="stat-number" id="total-logs">0</div>
            </div>
            <div class="stat-card errors">
                <h3>Errors</h3>
                <div class="stat-number" id="errors-count">0</div>
            </div>
            <div class="stat-card warnings">
                <h3>Warnings</h3>
                <div class="stat-number" id="warnings-count">0</div>
            </div>
            <div class="stat-card info">
                <h3>Info</h3>
                <div class="stat-number" id="info-count">0</div>
            </div>
        </div>

        <!-- Logs Section -->
        <div class="logs-section">
            <div class="section-header">
                Recent System Logs
            </div>

            <!-- Filters -->
            <div class="filters">
                <div class="filter-group">
                    <label>Level</label>
                    <select id="level-filter">
                        <option value="">All Levels</option>
                        <option value="info">Info</option>
                        <option value="warning">Warning</option>
                        <option value="error">Error</option>
                        <option value="debug">Debug</option>
                        <option value="critical">Critical</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Category</label>
                    <select id="category-filter">
                        <option value="">All Categories</option>
                        <option value="auth">Authentication</option>
                        <option value="admin">Admin</option>
                        <option value="api">API</option>
                        <option value="database">Database</option>
                        <option value="security">Security</option>
                        <option value="system">System</option>
                        <option value="upload">Upload</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label>Search</label>
                    <input type="text" id="search-input" placeholder="Search logs...">
                </div>
            </div>

            <!-- Logs Table -->
            <div id="logs-content">
                <div class="loading">Loading logs...</div>
            </div>

            <!-- Pagination -->
            <div class="pagination" id="pagination" style="display: none;">
                <button id="prev-page">Previous</button>
                <span id="page-info">Page 1 of 1</span>
                <button id="next-page">Next</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const API_BASE = 'http://localhost:5000/api';
        let currentPage = 1;
        let totalPages = 1;
        
        // Get auth token (you'll need to implement your auth system)
        function getAuthToken() {
            // Replace this with your actual token retrieval method
            return localStorage.getItem('authToken') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2NlOWVlMmJmNGQ4MGJiYmQwYzg5ZCIsImlhdCI6MTc0ODgyNjk2MCwiZXhwIjoxNzUxNDE4OTYwfQ.ljq9SiYZH_MLkPux_NY-Q6OrsXRGeakyk8w76sHQ0co';
        }

        // Fetch log statistics
        async function fetchLogStats() {
            try {
                const response = await fetch(`${API_BASE}/admin/logs/stats`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.data.summary;
                    
                    // Update the UI elements
                    document.getElementById('total-logs').textContent = stats.totalLogs.toLocaleString();
                    document.getElementById('errors-count').textContent = stats.errors.toLocaleString();
                    document.getElementById('warnings-count').textContent = stats.warnings.toLocaleString();
                    document.getElementById('info-count').textContent = stats.info.toLocaleString();
                } else {
                    console.error('Failed to fetch log stats:', data.message);
                }
            } catch (error) {
                console.error('Error fetching log stats:', error);
                showError('Failed to load log statistics');
            }
        }

        // Fetch logs with filters
        async function fetchLogs(page = 1) {
            try {
                const level = document.getElementById('level-filter').value;
                const category = document.getElementById('category-filter').value;
                const search = document.getElementById('search-input').value;
                
                const params = new URLSearchParams({
                    page: page,
                    limit: 20
                });
                
                if (level) params.append('level', level);
                if (category) params.append('category', category);
                if (search) params.append('search', search);
                
                const response = await fetch(`${API_BASE}/admin/logs?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`
                    }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayLogs(data.data.logs);
                    updatePagination(data.data.pagination);
                } else {
                    showError('Failed to fetch logs: ' + data.message);
                }
            } catch (error) {
                console.error('Error fetching logs:', error);
                showError('Failed to load logs');
            }
        }

        // Display logs in table
        function displayLogs(logs) {
            const content = document.getElementById('logs-content');
            
            if (logs.length === 0) {
                content.innerHTML = '<div class="loading">No logs found</div>';
                return;
            }
            
            const table = `
                <table class="logs-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Level</th>
                            <th>Category</th>
                            <th>Action</th>
                            <th>Message</th>
                            <th>User</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${logs.map(log => `
                            <tr>
                                <td>${new Date(log.createdAt).toLocaleString()}</td>
                                <td><span class="log-level ${log.level}">${log.level}</span></td>
                                <td>${log.category}</td>
                                <td>${log.action}</td>
                                <td>${log.message}</td>
                                <td>${log.userId ? (log.userId.firstName + ' ' + log.userId.lastName) : 'System'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;
            
            content.innerHTML = table;
        }

        // Update pagination
        function updatePagination(pagination) {
            currentPage = pagination.current;
            totalPages = pagination.pages;
            
            document.getElementById('page-info').textContent = `Page ${currentPage} of ${totalPages}`;
            document.getElementById('prev-page').disabled = currentPage <= 1;
            document.getElementById('next-page').disabled = currentPage >= totalPages;
            document.getElementById('pagination').style.display = 'block';
        }

        // Show error message
        function showError(message) {
            const content = document.getElementById('logs-content');
            content.innerHTML = `<div class="error-message">${message}</div>`;
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Load initial data
            fetchLogStats();
            fetchLogs();
            
            // Filter event listeners
            document.getElementById('level-filter').addEventListener('change', () => fetchLogs(1));
            document.getElementById('category-filter').addEventListener('change', () => fetchLogs(1));
            
            // Search with debounce
            let searchTimeout;
            document.getElementById('search-input').addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => fetchLogs(1), 500);
            });
            
            // Pagination
            document.getElementById('prev-page').addEventListener('click', () => {
                if (currentPage > 1) fetchLogs(currentPage - 1);
            });
            
            document.getElementById('next-page').addEventListener('click', () => {
                if (currentPage < totalPages) fetchLogs(currentPage + 1);
            });
            
            // Auto-refresh every 30 seconds
            setInterval(() => {
                fetchLogStats();
                fetchLogs(currentPage);
            }, 30000);
        });
    </script>
</body>
</html>
