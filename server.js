require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const session = require('express-session');

// Import security middleware
const {
  helmetConfig,
  sessionConfig,
  apiLimiter,
  securityHeaders,
  securityLogger,
  securityMonitor
} = require('./middleware/security');

// Import routes
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const foodAnalysisRoutes = require('./routes/foodAnalysis.routes');
const foodDatabaseRoutes = require('./routes/foodDatabase.routes');
const announcementsRoutes = require('./routes/announcements.routes');
const galleryRoutes = require('./routes/gallery.routes');

// Import admin routes
const adminRoutes = require('./routes/admin.routes');
const adminUsersRoutes = require('./routes/admin.users.routes');
const adminAnnouncementsRoutes = require('./routes/admin.announcements.routes');
const adminGalleryRoutes = require('./routes/admin.gallery.routes');
const adminConfigRoutes = require('./routes/admin.config.routes');
const adminLogsRoutes = require('./routes/admin.logs.routes');

// Create Express app
const app = express();

// Security middleware
app.use(helmetConfig);
app.use(securityHeaders);
app.use(securityLogger);
app.use(securityMonitor);
app.use(session(sessionConfig));

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));
app.use('/uploads', (req, res, next) => {
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
}, express.static(path.join(__dirname, 'public/uploads')));

// Serve static files from public directory
app.use('/public', express.static(path.join(__dirname, 'public')));


// Rate limiting for API routes
app.use('/api', (req, res, next) => {
  console.log(`Rate limiter hit: ${req.method} ${req.url}`);
  next();
}, apiLimiter);

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nutrisnap', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

// Public routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/food-analysis', foodAnalysisRoutes);
app.use('/api/food-database', foodDatabaseRoutes);
app.use('/api/announcements', announcementsRoutes);
app.use('/api/gallery', galleryRoutes);

// Admin routes
app.use('/api/admin', adminRoutes);
app.use('/api/admin/users', adminUsersRoutes);
app.use('/api/admin/announcements', adminAnnouncementsRoutes);
app.use('/api/admin/gallery', adminGalleryRoutes);
app.use('/api/admin/config', adminConfigRoutes);
app.use('/api/admin/logs', adminLogsRoutes);

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working'
  });
});

// Public system configuration route
app.get('/api/config-public', async (req, res) => {
  try {
    const SystemConfig = require('./models/SystemConfig');
    const publicConfigs = await SystemConfig.getPublicConfigs();

    res.json({
      success: true,
      data: publicConfigs
    });
  } catch (error) {
    console.error('Error in get public configs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Another test route
app.get('/api/public-config', async (req, res) => {
  console.log('PUBLIC CONFIG ROUTE HIT!');
  res.json({
    success: true,
    message: 'Public config route working',
    data: {
      site_title: 'NutriSnap - Smart Food Analysis',
      site_description: 'Analyze your food with AI-powered nutrition tracking'
    }
  });
});

// Public visitor count endpoint for landing page
app.get('/api/visitor-count', async (req, res) => {
  try {
    const VisitorTracking = require('./models/VisitorTracking');

    // Get current visitor statistics
    const onlineCount = await VisitorTracking.getOnlineCount();
    const todayStats = await VisitorTracking.getDailyStats(new Date());

    // Get total unique visitors (all time)
    const totalUniqueVisitors = await VisitorTracking.distinct('sessionId').then(sessions => sessions.length);

    // Get this week's stats
    const thisWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const weeklyStats = await VisitorTracking.aggregate([
      {
        $match: {
          createdAt: { $gte: thisWeek }
        }
      },
      {
        $group: {
          _id: null,
          weeklyVisitors: { $addToSet: '$sessionId' },
          weeklyPageViews: { $sum: '$pageViews' }
        }
      },
      {
        $project: {
          weeklyVisitors: { $size: '$weeklyVisitors' },
          weeklyPageViews: 1
        }
      }
    ]);

    const weeklyData = weeklyStats[0] || { weeklyVisitors: 0, weeklyPageViews: 0 };

    res.json({
      success: true,
      data: {
        online: onlineCount,
        today: {
          visitors: todayStats.uniqueVisitors,
          pageViews: todayStats.totalPageViews
        },
        weekly: {
          visitors: weeklyData.weeklyVisitors,
          pageViews: weeklyData.weeklyPageViews
        },
        total: {
          uniqueVisitors: totalUniqueVisitors
        },
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in visitor count endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Unable to fetch visitor statistics'
    });
  }
});

// Visitor tracking endpoint
app.post('/api/visitor-track', async (req, res) => {
  try {
    const VisitorTracking = require('./models/VisitorTracking');

    // Get visitor info from request
    const ip = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'] || 'unknown';
    const userAgent = req.headers['user-agent'] || 'unknown';
    const sessionId = req.sessionID || `session_${Date.now()}_${Math.random()}`;

    // Track the visitor
    await VisitorTracking.trackVisitor({
      sessionId,
      ip,
      userAgent,
      page: req.body.page || '/',
      referrer: req.body.referrer || req.headers.referer || null
    });

    res.json({
      success: true,
      message: 'Visitor tracked successfully'
    });

  } catch (error) {
    console.error('Error tracking visitor:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track visitor'
    });
  }
});

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
    availableRoutes: [
      'GET /api/test',
      'POST /api/auth/login',
      'POST /api/auth/register',
      'GET /api/gallery',
      'GET /api/announcements',
      'GET /api/config-public',
      'GET /api/public-config',
      'GET /api/visitor-count',
      'POST /api/visitor-track'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
