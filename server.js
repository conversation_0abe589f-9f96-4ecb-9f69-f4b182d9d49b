require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const session = require('express-session');

// Import security middleware
const {
  helmetConfig,
  sessionConfig,
  apiLimiter,
  securityHeaders,
  securityLogger
} = require('./middleware/security');

// Import routes
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const foodAnalysisRoutes = require('./routes/foodAnalysis.routes');
const foodDatabaseRoutes = require('./routes/foodDatabase.routes');
const announcementsRoutes = require('./routes/announcements.routes');
const galleryRoutes = require('./routes/gallery.routes');

// Import admin routes
const adminRoutes = require('./routes/admin.routes');
const adminUsersRoutes = require('./routes/admin.users.routes');
const adminAnnouncementsRoutes = require('./routes/admin.announcements.routes');
const adminGalleryRoutes = require('./routes/admin.gallery.routes');
const adminConfigRoutes = require('./routes/admin.config.routes');
const adminLogsRoutes = require('./routes/admin.logs.routes');

// Create Express app
const app = express();

// Security middleware
app.use(helmetConfig);
app.use(securityHeaders);
app.use(securityLogger);
app.use(session(sessionConfig));

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));
app.use('/uploads', (req, res, next) => {
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
}, express.static(path.join(__dirname, 'public/uploads')));


// Rate limiting for API routes
app.use('/api', (req, res, next) => {
  console.log(`Rate limiter hit: ${req.method} ${req.url}`);
  next();
}, apiLimiter);

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nutrisnap', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

// Public routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/food-analysis', foodAnalysisRoutes);
app.use('/api/food-database', foodDatabaseRoutes);
app.use('/api/announcements', announcementsRoutes);
app.use('/api/gallery', galleryRoutes);

// Admin routes
app.use('/api/admin', adminRoutes);
app.use('/api/admin/users', adminUsersRoutes);
app.use('/api/admin/announcements', adminAnnouncementsRoutes);
app.use('/api/admin/gallery', adminGalleryRoutes);
app.use('/api/admin/config', adminConfigRoutes);
app.use('/api/admin/logs', adminLogsRoutes);

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working'
  });
});

// Public system configuration route
app.get('/api/config-public', async (req, res) => {
  try {
    const SystemConfig = require('./models/SystemConfig');
    const publicConfigs = await SystemConfig.getPublicConfigs();

    res.json({
      success: true,
      data: publicConfigs
    });
  } catch (error) {
    console.error('Error in get public configs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Another test route
app.get('/api/public-config', async (req, res) => {
  console.log('PUBLIC CONFIG ROUTE HIT!');
  res.json({
    success: true,
    message: 'Public config route working',
    data: {
      site_title: 'NutriSnap - Smart Food Analysis',
      site_description: 'Analyze your food with AI-powered nutrition tracking'
    }
  });
});

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
    availableRoutes: [
      'GET /api/test',
      'POST /api/auth/login',
      'POST /api/auth/register',
      'GET /api/gallery',
      'GET /api/announcements',
      'GET /api/config-public',
      'GET /api/public-config'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
  });
});

// Start server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
