require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const session = require('express-session');
const { createServer } = require('http');
const { Server } = require('socket.io');

// Import security middleware
const {
  helmetConfig,
  sessionConfig,
  apiLimiter,
  securityHeaders,
  securityLogger,
  securityMonitor
} = require('./middleware/security');

// Import routes
const authRoutes = require('./routes/auth.routes');
const userRoutes = require('./routes/user.routes');
const foodAnalysisRoutes = require('./routes/foodAnalysis.routes');
const foodDatabaseRoutes = require('./routes/foodDatabase.routes');
const announcementsRoutes = require('./routes/announcements.routes');
const galleryRoutes = require('./routes/gallery.routes');

// Import admin routes
const adminRoutes = require('./routes/admin.routes');
const adminUsersRoutes = require('./routes/admin.users.routes');
const adminAnnouncementsRoutes = require('./routes/admin.announcements.routes');
const adminGalleryRoutes = require('./routes/admin.gallery.routes');
const adminConfigRoutes = require('./routes/admin.config.routes');
const adminLogsRoutes = require('./routes/admin.logs.routes');

// Create Express app
const app = express();

// Security middleware
app.use(helmetConfig);
app.use(securityHeaders);
app.use(securityLogger);
app.use(securityMonitor);
app.use(session(sessionConfig));

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(morgan('dev'));
app.use('/uploads', (req, res, next) => {
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
}, express.static(path.join(__dirname, 'public/uploads')));

// Serve static files from public directory
app.use('/public', express.static(path.join(__dirname, 'public')));


// Rate limiting for API routes
app.use('/api', (req, res, next) => {
  console.log(`Rate limiter hit: ${req.method} ${req.url}`);
  next();
}, apiLimiter);

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nutrisnap', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(() => console.log('MongoDB connected'))
.catch(err => console.error('MongoDB connection error:', err));

// Public routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/food-analysis', foodAnalysisRoutes);
app.use('/api/food-database', foodDatabaseRoutes);
app.use('/api/announcements', announcementsRoutes);
app.use('/api/gallery', galleryRoutes);

// Admin routes
app.use('/api/admin', adminRoutes);
app.use('/api/admin/users', adminUsersRoutes);
app.use('/api/admin/announcements', adminAnnouncementsRoutes);
app.use('/api/admin/gallery', adminGalleryRoutes);
app.use('/api/admin/config', adminConfigRoutes);
app.use('/api/admin/logs', adminLogsRoutes);

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API is working'
  });
});

// Public system configuration route
app.get('/api/config-public', async (req, res) => {
  try {
    const SystemConfig = require('./models/SystemConfig');
    const publicConfigs = await SystemConfig.getPublicConfigs();

    res.json({
      success: true,
      data: publicConfigs
    });
  } catch (error) {
    console.error('Error in get public configs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// Another test route
app.get('/api/public-config', async (req, res) => {
  console.log('PUBLIC CONFIG ROUTE HIT!');
  res.json({
    success: true,
    message: 'Public config route working',
    data: {
      site_title: 'NutriSnap - Smart Food Analysis',
      site_description: 'Analyze your food with AI-powered nutrition tracking'
    }
  });
});

// Dynamic sitemap.xml generation for SEO
app.get('/sitemap.xml', async (req, res) => {
  try {
    const Gallery = require('./models/Gallery');
    const Announcement = require('./models/Announcement');

    // Set content type to XML
    res.set('Content-Type', 'application/xml');

    // Get current date for lastmod
    const now = new Date().toISOString();
    const baseUrl = req.protocol + '://' + req.get('host');

    // Get active gallery items
    const galleryItems = await Gallery.find({ isActive: true })
      .select('slug updatedAt')
      .sort({ updatedAt: -1 });

    // Get active announcements
    const announcements = await Announcement.find({
      publishDate: { $lte: new Date() },
      $or: [
        { expiryDate: { $gte: new Date() } },
        { expiryDate: null }
      ]
    }).select('_id updatedAt').sort({ updatedAt: -1 });

    // Build sitemap XML
    let sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Main Pages -->
  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${now}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/gallery</loc>
    <lastmod>${galleryItems.length > 0 ? galleryItems[0].updatedAt.toISOString() : now}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${baseUrl}/announcements</loc>
    <lastmod>${announcements.length > 0 ? announcements[0].updatedAt.toISOString() : now}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${baseUrl}/about</loc>
    <lastmod>${now}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>
  <url>
    <loc>${baseUrl}/contact</loc>
    <lastmod>${now}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`;

    // Add gallery items
    galleryItems.forEach(item => {
      sitemap += `
  <url>
    <loc>${baseUrl}/gallery/${item.slug}</loc>
    <lastmod>${item.updatedAt.toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`;
    });

    // Add announcements
    announcements.forEach(announcement => {
      sitemap += `
  <url>
    <loc>${baseUrl}/announcements/${announcement._id}</loc>
    <lastmod>${announcement.updatedAt.toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`;
    });

    sitemap += `
</urlset>`;

    res.send(sitemap);

  } catch (error) {
    console.error('Error generating sitemap:', error);
    res.status(500).send('<?xml version="1.0" encoding="UTF-8"?><error>Unable to generate sitemap</error>');
  }
});

// Robots.txt for SEO
app.get('/robots.txt', (req, res) => {
  const baseUrl = req.protocol + '://' + req.get('host');

  const robotsTxt = `User-agent: *
Allow: /
Allow: /gallery
Allow: /announcements
Allow: /about
Allow: /contact
Allow: /api/gallery
Allow: /api/announcements
Allow: /api/config-public

# Disallow admin and private areas
Disallow: /api/admin/
Disallow: /api/auth/
Disallow: /api/users/
Disallow: /api/food-analysis/
Disallow: /admin/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;

  res.set('Content-Type', 'text/plain');
  res.send(robotsTxt);
});

// Sitemap index for better organization
app.get('/sitemap-index.xml', (req, res) => {
  const baseUrl = req.protocol + '://' + req.get('host');
  const now = new Date().toISOString();

  const sitemapIndex = `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <sitemap>
    <loc>${baseUrl}/sitemap.xml</loc>
    <lastmod>${now}</lastmod>
  </sitemap>
</sitemapindex>`;

  res.set('Content-Type', 'application/xml');
  res.send(sitemapIndex);
});

// Public visitor count endpoint for landing page
app.get('/api/visitor-count', async (req, res) => {
  try {
    const VisitorTracking = require('./models/VisitorTracking');

    // Get current visitor statistics
    const onlineCount = await VisitorTracking.getOnlineCount();
    const todayStats = await VisitorTracking.getDailyStats(new Date());

    // Get total unique visitors (all time)
    const totalUniqueVisitors = await VisitorTracking.distinct('sessionId').then(sessions => sessions.length);

    // Get this week's stats
    const thisWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const weeklyStats = await VisitorTracking.aggregate([
      {
        $match: {
          createdAt: { $gte: thisWeek }
        }
      },
      {
        $group: {
          _id: null,
          weeklyVisitors: { $addToSet: '$sessionId' },
          weeklyPageViews: { $sum: '$pageViews' }
        }
      },
      {
        $project: {
          weeklyVisitors: { $size: '$weeklyVisitors' },
          weeklyPageViews: 1
        }
      }
    ]);

    const weeklyData = weeklyStats[0] || { weeklyVisitors: 0, weeklyPageViews: 0 };

    res.json({
      success: true,
      data: {
        online: onlineCount,
        today: {
          visitors: todayStats.uniqueVisitors,
          pageViews: todayStats.totalPageViews
        },
        weekly: {
          visitors: weeklyData.weeklyVisitors,
          pageViews: weeklyData.weeklyPageViews
        },
        total: {
          uniqueVisitors: totalUniqueVisitors
        },
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error in visitor count endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Unable to fetch visitor statistics'
    });
  }
});

// Public visitor tracking endpoint
app.post('/api/visitor-track', async (req, res) => {
  try {
    const VisitorTracking = require('./models/VisitorTracking');
    const { page } = req.body;

    // Get or create session ID (simple implementation)
    const sessionId = req.sessionID || `session_${Date.now()}_${Math.random()}`;

    // Track the visitor
    await VisitorTracking.trackVisitor(sessionId, page);

    res.json({
      success: true,
      message: 'Visitor tracked successfully'
    });

  } catch (error) {
    console.error('Error in visitor tracking endpoint:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to track visitor'
    });
  }
});

// 404 handler for unmatched routes
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`,
    availableRoutes: [
      'GET /api/test',
      'POST /api/auth/login',
      'POST /api/auth/register',
      'GET /api/gallery',
      'GET /api/announcements',
      'GET /api/config-public',
      'GET /api/public-config',
      'GET /api/visitor-count',
      'GET /sitemap.xml',
      'GET /robots.txt'
    ]
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
  });
});

// Create HTTP server and Socket.IO
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: ["http://localhost:8080", "http://localhost:8081"],
    methods: ["GET", "POST"]
  }
});

// Track online users
let onlineUsers = new Set();

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);

  // Add user to online set
  onlineUsers.add(socket.id);

  // Broadcast updated count to all clients
  io.emit('onlineCount', onlineUsers.size);

  // Handle heartbeat to keep connection alive
  socket.on('heartbeat', () => {
    // Just acknowledge the heartbeat
    socket.emit('heartbeat-ack');
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    onlineUsers.delete(socket.id);

    // Broadcast updated count to all clients
    io.emit('onlineCount', onlineUsers.size);
  });
});

// Start server
const PORT = process.env.PORT || 5001; // Changed default to 5001
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('WebSocket server ready for real-time connections');
});
