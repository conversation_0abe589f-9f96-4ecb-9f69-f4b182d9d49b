const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to verify JWT token
exports.protect = async (req, res, next) => {
  let token;

  // Check if token exists in headers
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  // Check if token exists
  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }

  try {
    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Find user by id
    const user = await User.findById(decoded.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Add user to request object
    req.user = user;
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Not authorized to access this route'
    });
  }
};

// Middleware to check if user is admin
exports.admin = (req, res, next) => {
  if (!req.user.isAdminRole()) {
    return res.status(403).json({
      success: false,
      message: 'Access denied: Admin only'
    });
  }
  next();
};

// Middleware to check if user is editor or admin
exports.editor = (req, res, next) => {
  if (!req.user.isEditorRole()) {
    return res.status(403).json({
      success: false,
      message: 'Access denied: Editor or Admin access required'
    });
  }
  next();
};

// Middleware to check if user can access admin panel
exports.adminPanel = (req, res, next) => {
  if (!req.user.canAccessAdminPanel()) {
    return res.status(403).json({
      success: false,
      message: 'Access denied: Admin panel access required'
    });
  }
  next();
};

// Middleware to check specific roles
exports.requireRole = (roles) => {
  return (req, res, next) => {
    if (!Array.isArray(roles)) {
      roles = [roles];
    }

    const userRole = req.user.role;
    const hasRole = roles.includes(userRole) || (req.user.isAdmin && roles.includes('admin'));

    if (!hasRole) {
      return res.status(403).json({
        success: false,
        message: `Access denied: Requires one of the following roles: ${roles.join(', ')}`
      });
    }
    next();
  };
};
