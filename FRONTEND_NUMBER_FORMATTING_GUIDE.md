# Frontend Number Formatting Guide

## Problem: Floating-Point Precision Issues

JavaScript floating-point arithmetic can result in numbers like `178.89999999999998` instead of `178.9`. This guide provides solutions for consistent number formatting in the frontend.

## Solution: Create Utility Functions

### 1. Create `utils/numberUtils.js` in Frontend

```javascript
/**
 * Frontend number utility functions for handling floating-point precision
 */

/**
 * Round a number to specified decimal places
 * @param {number} num - The number to round
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {number} - Properly rounded number
 */
export function roundToDecimals(num, decimals = 1) {
  if (typeof num !== 'number' || isNaN(num)) {
    return 0;
  }
  
  const factor = Math.pow(10, decimals);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

/**
 * Round nutritional values consistently
 * @param {number} value - The nutritional value to round
 * @returns {number} - Rounded value (1 decimal place for precision)
 */
export function roundNutritionalValue(value) {
  return roundToDecimals(value, 1);
}

/**
 * Round calorie values (typically whole numbers)
 * @param {number} calories - The calorie value to round
 * @returns {number} - Rounded calories (whole number)
 */
export function roundCalories(calories) {
  return Math.round(calories || 0);
}

/**
 * Format nutritional value for display
 * @param {number} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} - Formatted string
 */
export function formatNutritionalValue(value, decimals = 1) {
  const rounded = roundToDecimals(value, decimals);
  
  // Remove trailing zeros for cleaner display
  if (decimals > 0) {
    return parseFloat(rounded.toFixed(decimals)).toString();
  }
  
  return rounded.toString();
}

/**
 * Format number for display with proper rounding
 * Examples:
 * - 178.89999999999998 → "178.9"
 * - 178.0 → "178"
 * - 178.12345 → "178.1"
 */
export function formatDisplayNumber(value, maxDecimals = 1) {
  if (typeof value !== 'number' || isNaN(value)) {
    return '0';
  }
  
  const rounded = roundToDecimals(value, maxDecimals);
  
  // Remove unnecessary trailing zeros
  return parseFloat(rounded.toFixed(maxDecimals)).toString();
}
```

### 2. Update Dashboard Component

```typescript
// Dashboard.tsx
import React from 'react';
import { formatDisplayNumber, roundCalories, roundNutritionalValue } from '../utils/numberUtils';

const Dashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);

  // When displaying nutritional values, use formatting functions
  return (
    <div className="dashboard">
      <div className="nutrition-cards">
        <div className="nutrition-card calories">
          <h3>Calories</h3>
          <div className="value">
            {formatDisplayNumber(dashboardData?.todaysNutrition?.calories?.current, 0)}
          </div>
          <div className="target">
            / {dashboardData?.todaysNutrition?.calories?.target}
          </div>
        </div>

        <div className="nutrition-card protein">
          <h3>Protein</h3>
          <div className="value">
            {formatDisplayNumber(dashboardData?.todaysNutrition?.protein?.current)}g
          </div>
        </div>

        <div className="nutrition-card carbs">
          <h3>Carbs</h3>
          <div className="value">
            {formatDisplayNumber(dashboardData?.todaysNutrition?.carbs?.current)}g
          </div>
        </div>

        <div className="nutrition-card fat">
          <h3>Fat</h3>
          <div className="value">
            {formatDisplayNumber(dashboardData?.todaysNutrition?.fat?.current)}g
          </div>
        </div>
      </div>
    </div>
  );
};
```

### 3. Update Nutrition Summary Component

```typescript
// NutritionSummary.tsx
import { formatDisplayNumber } from '../utils/numberUtils';

const NutritionSummary = () => {
  return (
    <div className="nutrition-summary">
      <div className="nutrition-cards">
        <div className="nutrition-card calories">
          <h3>Calories</h3>
          <div className="value">
            {formatDisplayNumber(summaryData?.summary?.totalCalories, 0)}
          </div>
          {summaryData?.averages && (
            <div className="average">
              Avg: {formatDisplayNumber(summaryData.averages.dailyCalories, 0)}/day
            </div>
          )}
        </div>

        <div className="nutrition-card protein">
          <h3>Protein</h3>
          <div className="value">
            {formatDisplayNumber(summaryData?.summary?.totalProtein)}g
          </div>
          {summaryData?.averages && (
            <div className="average">
              Avg: {formatDisplayNumber(summaryData.averages.dailyProtein)}g/day
            </div>
          )}
        </div>

        <div className="nutrition-card carbs">
          <h3>Carbohydrates</h3>
          <div className="value">
            {formatDisplayNumber(summaryData?.summary?.totalCarbohydrates)}g
          </div>
        </div>

        <div className="nutrition-card fats">
          <h3>Fats</h3>
          <div className="value">
            {formatDisplayNumber(summaryData?.summary?.totalFats)}g
          </div>
        </div>
      </div>
    </div>
  );
};
```

### 4. Update Meal History Component

```typescript
// MealHistory.tsx
import { formatDisplayNumber } from '../utils/numberUtils';

const MealHistory = () => {
  return (
    <div className="meal-history">
      {meals.map(meal => (
        <div key={meal.id} className="meal-card">
          <div className="meal-nutrition">
            <div className="nutrition-item">
              <span className="label">Calories</span>
              <span className="value">
                {formatDisplayNumber(meal.nutritionalSummary.totalCalories, 0)}
              </span>
            </div>
            <div className="nutrition-item">
              <span className="label">Protein</span>
              <span className="value">
                {formatDisplayNumber(meal.nutritionalSummary.totalProtein)}g
              </span>
            </div>
            <div className="nutrition-item">
              <span className="label">Carbs</span>
              <span className="value">
                {formatDisplayNumber(meal.nutritionalSummary.totalCarbohydrates)}g
              </span>
            </div>
            <div className="nutrition-item">
              <span className="label">Fats</span>
              <span className="value">
                {formatDisplayNumber(meal.nutritionalSummary.totalFats)}g
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};
```

### 5. Update Analysis Results Component

```typescript
// SnapNew.tsx or AnalysisResults component
import { formatDisplayNumber } from '../utils/numberUtils';

const AnalysisResults = ({ analysisData }) => {
  return (
    <div className="analysis-results">
      <div className="nutrition-preview">
        <h4>Nutrition Summary</h4>
        <div className="nutrition-grid">
          <div className="nutrition-item">
            <span className="label">Calories</span>
            <span className="value">
              {formatDisplayNumber(analysisData?.totals?.total_calories, 0)}
            </span>
          </div>
          <div className="nutrition-item">
            <span className="label">Protein</span>
            <span className="value">
              {formatDisplayNumber(analysisData?.totals?.total_protein)}g
            </span>
          </div>
          <div className="nutrition-item">
            <span className="label">Carbs</span>
            <span className="value">
              {formatDisplayNumber(analysisData?.totals?.total_carbohydrates)}g
            </span>
          </div>
          <div className="nutrition-item">
            <span className="label">Fats</span>
            <span className="value">
              {formatDisplayNumber(analysisData?.totals?.total_fats)}g
            </span>
          </div>
        </div>
      </div>

      <div className="food-items">
        {analysisData?.food_items?.map((item, index) => (
          <div key={index} className="food-item">
            <span className="food-name">{item.name}</span>
            <span className="food-calories">
              {formatDisplayNumber(parseFloat(item.calories), 0)} cal
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

## Key Benefits

1. **Consistent Formatting**: All nutritional values display consistently
2. **Clean Numbers**: `178.89999999999998` becomes `178.9`
3. **Appropriate Precision**: Calories as whole numbers, nutrients with 1 decimal
4. **No Trailing Zeros**: `178.0` becomes `178`
5. **Error Handling**: Gracefully handles null/undefined values

## Implementation Steps

1. Create the `utils/numberUtils.js` file in your frontend
2. Import the formatting functions in components that display nutritional data
3. Replace direct number display with `formatDisplayNumber()` calls
4. Test with various nutritional values to ensure proper formatting

This will resolve the floating-point precision issues and provide clean, consistent number display throughout the application.
