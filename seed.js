require('dotenv').config();
const mongoose = require('mongoose');
const User = require('./models/User');
const FoodDatabase = require('./models/FoodDatabase');
const Announcement = require('./models/Announcement');
const SystemConfig = require('./models/SystemConfig');
const Gallery = require('./models/Gallery');

// Sample data
const users = [
  {
    email: '<EMAIL>',
    password: '123456',
    firstName: 'John',
    lastName: 'Cabsho',
    role: 'user',
    status: 'active',
    isAdmin: false,
    preferences: {
      dietaryRestrictions: ['vegetarian'],
      nutritionGoals: {
        calories: 2000,
        protein: 150,
        carbs: 200,
        fat: 65
      },
      units: 'metric'
    }
  },
  {
    email: '<EMAIL>',
    password: 'password',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    status: 'active',
    isAdmin: true,
    preferences: {
      dietaryRestrictions: [],
      nutritionGoals: {
        calories: 2200,
        protein: 180,
        carbs: 220,
        fat: 70
      },
      units: 'imperial'
    }
  },
  {
    email: '<EMAIL>',
    password: 'password',
    firstName: 'Content',
    lastName: 'Editor',
    role: 'editor',
    status: 'active',
    isAdmin: false,
    preferences: {
      dietaryRestrictions: [],
      nutritionGoals: {
        calories: 2100,
        protein: 160,
        carbs: 210,
        fat: 75
      },
      units: 'metric'
    }
  },
  {
    email: '<EMAIL>',
    password: 'password',
    firstName: 'Suspended',
    lastName: 'User',
    role: 'user',
    status: 'suspended',
    isAdmin: false,
    preferences: {
      dietaryRestrictions: [],
      nutritionGoals: {
        calories: 2000,
        protein: 120,
        carbs: 250,
        fat: 70
      },
      units: 'metric'
    }
  },
  {
    email: '<EMAIL>',
    password: 'password',
    firstName: 'Inactive',
    lastName: 'User',
    role: 'user',
    status: 'inactive',
    isAdmin: false,
    preferences: {
      dietaryRestrictions: [],
      nutritionGoals: {
        calories: 2000,
        protein: 120,
        carbs: 250,
        fat: 70
      },
      units: 'metric'
    }
  }
];

const foodItems = [
  {
    name: 'Apple',
    category: 'Fruits',
    nutrientsPerServing: {
      servingSize: '1 medium (182g)',
      calories: 95,
      protein: 0.5,
      carbs: 25,
      fat: 0.3,
      fiber: 4.4,
      sugar: 19,
      sodium: 2,
      vitamin_a: 2,
      vitamin_c: 14,
      calcium: 1,
      iron: 1
    },
    commonPortions: [
      '1 small',
      '1 medium',
      '1 large',
      '1 cup, sliced'
    ]
  },
  {
    name: 'Grilled Chicken Breast',
    category: 'Poultry',
    nutrientsPerServing: {
      servingSize: '1 breast (172g)',
      calories: 284,
      protein: 53.4,
      carbs: 0,
      fat: 6.2,
      fiber: 0,
      sugar: 0,
      sodium: 130,
      vitamin_a: 0,
      vitamin_c: 0,
      calcium: 1,
      iron: 5
    },
    commonPortions: [
      '1/2 breast',
      '1 breast',
      '100g'
    ]
  },
  {
    name: 'Avocado',
    category: 'Fruits',
    nutrientsPerServing: {
      servingSize: '1 avocado (200g)',
      calories: 322,
      protein: 4,
      carbs: 17,
      fat: 30,
      fiber: 13.5,
      sugar: 1.3,
      sodium: 14,
      vitamin_a: 4,
      vitamin_c: 33,
      calcium: 2,
      iron: 6
    },
    commonPortions: [
      '1/4 avocado',
      '1/2 avocado',
      '1 whole avocado'
    ]
  }
];

const announcements = [
  {
    title: 'New Food Recognition Engine',
    content: 'We\'ve upgraded our AI food recognition system to identify over 5,000 food items with greater accuracy. Try it out today!',
    category: 'feature',
    importance: 'high',
    publishDate: new Date('2023-04-30'),
    expiryDate: new Date('2023-06-30')
  },
  {
    title: 'Summer Challenge Starting Soon',
    content: 'Join our 30-day summer nutrition challenge starting June 1st. Track your meals consistently to win prizes and improve your health!',
    category: 'event',
    importance: 'medium',
    publishDate: new Date('2023-05-15'),
    expiryDate: new Date('2023-06-01')
  }
];

const galleryItems = [
  {
    title: 'Bariir iyo hilib',
    description: 'Bariir iyo hilib waxay kamid tahay cuntooyin ka mid ah Somalia',
    imageUrl: 'http://localhost:5000/uploads/bariir-iyo-hilib.png',
    filename: 'bariir-iyo-hilib.png',
    originalName: 'bariir.png',
    mimeType: 'image/png',
    size: 1204290,
    slug: 'bariir-iyo-hilib',
    category: 'food',
    tags: ['food', 'traditional', 'somalia'],
    altText: 'Bariir iyo hilib',
    isActive: true,
    isFeatured: true,
    displayOrder: 1
  },
  {
    title: 'Pizza Geel',
    description: 'Pizza with geel is the most wanted food in somalia',
    imageUrl: 'http://localhost:5000/uploads/pizza-geel.png',
    filename: 'pizza-geel.png',
    originalName: 'pizza.png',
    mimeType: 'image/png',
    size: 1752,
    slug: 'pizza-geel',
    category: 'food',
    tags: ['fastfood', 'pizza'],
    altText: 'Pizza Geel',
    isActive: true,
    isFeatured: false,
    displayOrder: 2
  },
  {
    title: '111',
    description: '1111',
    imageUrl: 'http://localhost:5000/uploads/111.png',
    filename: '111.png',
    originalName: 'sample.png',
    mimeType: 'image/png',
    size: 457963,
    slug: '111',
    category: 'general',
    tags: ['sample'],
    altText: '111',
    isActive: true,
    isFeatured: false,
    displayOrder: 3
  }
];

const systemConfigs = [
  {
    key: 'site_title',
    value: 'NutriSnap - Smart Food Analysis',
    type: 'string',
    category: 'general',
    description: 'Main site title displayed in browser and headers',
    isPublic: true,
    isEditable: true
  },
  {
    key: 'site_description',
    value: 'Analyze your food with AI-powered nutrition tracking',
    type: 'string',
    category: 'general',
    description: 'Site description for SEO and meta tags',
    isPublic: true,
    isEditable: true
  },
  {
    key: 'max_upload_size',
    value: 5242880,
    type: 'number',
    category: 'api',
    description: 'Maximum file upload size in bytes (5MB)',
    isPublic: false,
    isEditable: true
  },
  {
    key: 'enable_user_registration',
    value: true,
    type: 'boolean',
    category: 'security',
    description: 'Allow new users to register accounts',
    isPublic: true,
    isEditable: true
  },
  {
    key: 'maintenance_mode',
    value: false,
    type: 'boolean',
    category: 'general',
    description: 'Enable maintenance mode to restrict access',
    isPublic: true,
    isEditable: true
  },
  {
    key: 'supported_languages',
    value: ['en', 'es', 'fr'],
    type: 'array',
    category: 'general',
    description: 'List of supported language codes',
    isPublic: true,
    isEditable: true
  }
];

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nutrisnap', {
  useNewUrlParser: true,
  useUnifiedTopology: true
})
.then(async () => {
  console.log('MongoDB connected');
  
  try {
    // Clear existing data
    await User.deleteMany({});
    await FoodDatabase.deleteMany({});
    await Announcement.deleteMany({});
    await SystemConfig.deleteMany({});
    await Gallery.deleteMany({});

    console.log('Data cleared');
    
    // Seed users
    const createdUsers = [];
    for (const user of users) {
      const createdUser = await User.create(user);
      createdUsers.push(createdUser);
    }

    console.log('Users seeded');
    
    // Seed food database
    await FoodDatabase.insertMany(foodItems);
    console.log('Food database seeded');
    
    // Seed announcements
    await Announcement.insertMany(announcements);
    console.log('Announcements seeded');

    // Seed system configurations
    await SystemConfig.insertMany(systemConfigs);
    console.log('System configurations seeded');

    // Seed gallery items (assign to admin user)
    const adminUser = createdUsers.find(user => user.email === '<EMAIL>');
    if (adminUser) {
      const galleryItemsWithUser = galleryItems.map(item => ({
        ...item,
        uploadedBy: adminUser._id
      }));
      await Gallery.insertMany(galleryItemsWithUser);
      console.log('Gallery items seeded');
    }

    console.log('Database seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);
    process.exit(1);
  }
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  process.exit(1);
});
