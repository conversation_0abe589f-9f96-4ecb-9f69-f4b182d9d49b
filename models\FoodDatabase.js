const mongoose = require('mongoose');

const NutrientsSchema = new mongoose.Schema({
  servingSize: {
    type: String,
    required: true
  },
  calories: {
    type: Number,
    required: true
  },
  protein: {
    type: Number,
    required: true
  },
  carbs: {
    type: Number,
    required: true
  },
  fat: {
    type: Number,
    required: true
  },
  fiber: {
    type: Number,
    required: true
  },
  sugar: {
    type: Number,
    required: true
  },
  sodium: {
    type: Number,
    required: true
  },
  vitamin_a: {
    type: Number,
    default: 0
  },
  vitamin_c: {
    type: Number,
    default: 0
  },
  calcium: {
    type: Number,
    default: 0
  },
  iron: {
    type: Number,
    default: 0
  }
});

const FoodDatabaseSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    index: true
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  nutrientsPerServing: NutrientsSchema,
  commonPortions: {
    type: [String],
    default: []
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Create text index for search functionality
FoodDatabaseSchema.index({ name: 'text', category: 'text' });

module.exports = mongoose.model('FoodDatabase', FoodDatabaseSchema);
