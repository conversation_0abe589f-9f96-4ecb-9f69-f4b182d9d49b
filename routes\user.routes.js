const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect } = require('../middleware/auth');
const User = require('../models/User');

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', protect, async (req, res) => {
  try {
    // User is already available in req.user from the protect middleware
    res.json({
      success: true,
      user: req.user
    });
  } catch (error) {
    console.error('Error in get profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/users/preferences
// @desc    Update user preferences
// @access  Private
router.put(
  '/preferences',
  protect,
  [
    check('dietaryRestrictions', 'Dietary restrictions must be an array').isArray(),
    check('nutritionGoals', 'Nutrition goals must be an object').isObject(),
    check('units', 'Units must be either metric or imperial').isIn(['metric', 'imperial'])
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { dietaryRestrictions, nutritionGoals, units } = req.body;

    try {
      // Find user and update preferences
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Update preferences
      user.preferences = {
        dietaryRestrictions: dietaryRestrictions || user.preferences.dietaryRestrictions,
        nutritionGoals: {
          calories: nutritionGoals?.calories || user.preferences.nutritionGoals.calories,
          protein: nutritionGoals?.protein || user.preferences.nutritionGoals.protein,
          carbs: nutritionGoals?.carbs || user.preferences.nutritionGoals.carbs,
          fat: nutritionGoals?.fat || user.preferences.nutritionGoals.fat
        },
        units: units || user.preferences.units
      };

      // Save updated user
      await user.save();

      res.json({
        success: true,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isAdmin: user.isAdmin,
          preferences: user.preferences
        }
      });
    } catch (error) {
      console.error('Error in update preferences:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

module.exports = router;
