const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect } = require('../middleware/auth');
const User = require('../models/User');
const bcrypt = require('bcryptjs');

// @route   GET /api/users/profile
// @desc    Get user profile
// @access  Private
router.get('/profile', protect, async (req, res) => {
  try {
    // User is already available in req.user from the protect middleware
    res.json({
      success: true,
      user: req.user
    });
  } catch (error) {
    console.error('Error in get profile:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/users/settings
// @desc    Update user settings (comprehensive)
// @access  Private
router.put(
  '/settings',
  protect,
  [
    check('dietaryRestrictions', 'Dietary restrictions must be an array').optional().isArray(),
    check('nutritionGoals', 'Nutrition goals must be an object').optional().isObject(),
    check('units', 'Units must be either metric or imperial').optional().isIn(['metric', 'imperial']),
    check('notifications', 'Notifications must be an object').optional().isObject(),
    check('currentPassword', 'Current password is required when changing password').optional().isLength({ min: 1 }),
    check('newPassword', 'New password must be at least 6 characters').optional().isLength({ min: 6 }),
    check('confirmPassword', 'Confirm password is required when changing password').optional().isLength({ min: 1 })
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const {
      dietaryRestrictions,
      nutritionGoals,
      units,
      notifications,
      currentPassword,
      newPassword,
      confirmPassword
    } = req.body;

    try {
      // Find user
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Handle password change if provided
      if (currentPassword && newPassword && confirmPassword) {
        // Verify current password
        const isCurrentPasswordValid = await user.comparePassword(currentPassword);
        if (!isCurrentPasswordValid) {
          return res.status(400).json({
            success: false,
            message: 'Current password is incorrect'
          });
        }

        // Check if new passwords match
        if (newPassword !== confirmPassword) {
          return res.status(400).json({
            success: false,
            message: 'New passwords do not match'
          });
        }

        // Update password
        user.password = newPassword;
      }

      // Update preferences if provided
      if (dietaryRestrictions !== undefined) {
        user.preferences.dietaryRestrictions = dietaryRestrictions;
      }

      if (nutritionGoals !== undefined) {
        user.preferences.nutritionGoals = {
          calories: nutritionGoals.calories || user.preferences.nutritionGoals.calories,
          protein: nutritionGoals.protein || user.preferences.nutritionGoals.protein,
          carbs: nutritionGoals.carbs || user.preferences.nutritionGoals.carbs,
          fat: nutritionGoals.fat || user.preferences.nutritionGoals.fat
        };
      }

      if (units !== undefined) {
        user.preferences.units = units;
      }

      if (notifications !== undefined) {
        user.preferences.notifications = {
          mealReminders: notifications.mealReminders !== undefined ? notifications.mealReminders : user.preferences.notifications.mealReminders,
          weeklyReports: notifications.weeklyReports !== undefined ? notifications.weeklyReports : user.preferences.notifications.weeklyReports,
          achievementAlerts: notifications.achievementAlerts !== undefined ? notifications.achievementAlerts : user.preferences.notifications.achievementAlerts,
          emailNotifications: notifications.emailNotifications !== undefined ? notifications.emailNotifications : user.preferences.notifications.emailNotifications
        };
      }

      // Save updated user
      await user.save();

      // Return user without password
      const userResponse = {
        id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        isAdmin: user.isAdmin,
        preferences: user.preferences
      };

      res.json({
        success: true,
        message: 'Settings updated successfully',
        user: userResponse
      });
    } catch (error) {
      console.error('Error in update settings:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   PUT /api/users/preferences
// @desc    Update user preferences (legacy endpoint)
// @access  Private
router.put(
  '/preferences',
  protect,
  [
    check('dietaryRestrictions', 'Dietary restrictions must be an array').optional().isArray(),
    check('nutritionGoals', 'Nutrition goals must be an object').optional().isObject(),
    check('units', 'Units must be either metric or imperial').optional().isIn(['metric', 'imperial'])
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { dietaryRestrictions, nutritionGoals, units } = req.body;

    try {
      // Find user and update preferences
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Update preferences
      if (dietaryRestrictions !== undefined) {
        user.preferences.dietaryRestrictions = dietaryRestrictions;
      }

      if (nutritionGoals !== undefined) {
        user.preferences.nutritionGoals = {
          calories: nutritionGoals.calories || user.preferences.nutritionGoals.calories,
          protein: nutritionGoals.protein || user.preferences.nutritionGoals.protein,
          carbs: nutritionGoals.carbs || user.preferences.nutritionGoals.carbs,
          fat: nutritionGoals.fat || user.preferences.nutritionGoals.fat
        };
      }

      if (units !== undefined) {
        user.preferences.units = units;
      }

      // Save updated user
      await user.save();

      res.json({
        success: true,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isAdmin: user.isAdmin,
          preferences: user.preferences
        }
      });
    } catch (error) {
      console.error('Error in update preferences:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   DELETE /api/users/account
// @desc    Delete user account
// @access  Private
router.delete(
  '/account',
  protect,
  [
    check('password', 'Password is required to delete account').isLength({ min: 1 })
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { password } = req.body;

    try {
      // Find user
      const user = await User.findById(req.user._id);

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Verify password
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(400).json({
          success: false,
          message: 'Incorrect password'
        });
      }

      // Delete user account
      await User.findByIdAndDelete(req.user._id);

      res.json({
        success: true,
        message: 'Account deleted successfully'
      });
    } catch (error) {
      console.error('Error in delete account:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

module.exports = router;
