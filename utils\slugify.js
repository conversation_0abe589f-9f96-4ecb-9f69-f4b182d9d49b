/**
 * Utility functions for generating SEO-friendly slugs
 */

/**
 * Convert string to URL-friendly slug
 * @param {string} text - Text to convert to slug
 * @param {object} options - Options for slug generation
 * @returns {string} - Generated slug
 */
function slugify(text, options = {}) {
  const defaults = {
    replacement: '-',
    remove: /[*+~.()'"!:@]/g,
    lower: true,
    strict: false,
    locale: 'en',
    trim: true
  };
  
  const opts = { ...defaults, ...options };
  
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  let slug = text;
  
  // Replace spaces and underscores with replacement character
  slug = slug.replace(/[\s_]+/g, opts.replacement);
  
  // Remove special characters
  if (opts.remove) {
    slug = slug.replace(opts.remove, '');
  }
  
  // Convert to lowercase
  if (opts.lower) {
    slug = slug.toLowerCase();
  }
  
  // Remove accents and special characters
  slug = slug.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
  
  // Replace non-alphanumeric characters (except replacement character)
  if (opts.strict) {
    const pattern = new RegExp(`[^a-zA-Z0-9\\${opts.replacement}]`, 'g');
    slug = slug.replace(pattern, '');
  }
  
  // Remove multiple consecutive replacement characters
  const multipleReplacementPattern = new RegExp(`\\${opts.replacement}+`, 'g');
  slug = slug.replace(multipleReplacementPattern, opts.replacement);
  
  // Trim replacement characters from start and end
  if (opts.trim) {
    const trimPattern = new RegExp(`^\\${opts.replacement}+|\\${opts.replacement}+$`, 'g');
    slug = slug.replace(trimPattern, '');
  }
  
  return slug;
}

/**
 * Generate unique slug for a model
 * @param {object} Model - Mongoose model
 * @param {string} text - Text to convert to slug
 * @param {string} field - Field name to check for uniqueness (default: 'slug')
 * @param {string} excludeId - ID to exclude from uniqueness check
 * @returns {string} - Unique slug
 */
async function generateUniqueSlug(Model, text, field = 'slug', excludeId = null) {
  let baseSlug = slugify(text);
  let slug = baseSlug;
  let counter = 1;
  
  if (!baseSlug) {
    baseSlug = 'item';
    slug = baseSlug;
  }
  
  while (true) {
    const query = { [field]: slug };
    
    // Exclude current document if updating
    if (excludeId) {
      query._id = { $ne: excludeId };
    }
    
    const existingDoc = await Model.findOne(query);
    
    if (!existingDoc) {
      break;
    }
    
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
}

/**
 * Generate slug from title with fallback
 * @param {string} title - Primary text for slug
 * @param {string} fallback - Fallback text if title is empty
 * @returns {string} - Generated slug
 */
function generateSlugFromTitle(title, fallback = 'untitled') {
  if (!title || title.trim() === '') {
    return slugify(fallback);
  }
  return slugify(title);
}

/**
 * Validate slug format
 * @param {string} slug - Slug to validate
 * @returns {boolean} - Whether slug is valid
 */
function isValidSlug(slug) {
  if (!slug || typeof slug !== 'string') {
    return false;
  }
  
  // Check if slug contains only allowed characters
  const validPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return validPattern.test(slug);
}

/**
 * Clean and format existing slug
 * @param {string} slug - Existing slug to clean
 * @returns {string} - Cleaned slug
 */
function cleanSlug(slug) {
  if (!slug) return '';
  
  return slugify(slug, {
    strict: true,
    lower: true,
    trim: true
  });
}

module.exports = {
  slugify,
  generateUniqueSlug,
  generateSlugFromTitle,
  isValidSlug,
  cleanSlug
};
