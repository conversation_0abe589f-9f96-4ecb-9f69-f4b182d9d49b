# 👥 NutriSnap Admin Panel - Role Features Guide

## 🎯 **Role Overview**

The NutriSnap admin panel implements a three-tier role-based access control system designed to provide appropriate access levels for different types of users.

---

## 🔵 **USER ROLE** - Basic App Access
**Target Audience:** Regular app users, customers, general public  
**Access Level:** Basic application features only

### ✅ **Core Features Available:**

#### 🍎 **Food Analysis & Tracking**
- Upload food images for AI-powered analysis
- View personal food analysis history with filtering
- Update meal categories and notes
- Delete personal food analyses
- Access detailed nutritional breakdowns
- Track daily/weekly nutrition progress

#### 👤 **Profile & Account Management**
- View and edit personal profile information
- Update dietary restrictions and preferences
- Modify nutrition goals (calories, protein, carbs, fat)
- Change measurement units (metric/imperial)
- Update password and account settings

#### 📖 **Public Content Access**
- View active announcements and news
- Browse public gallery images
- Search food database for nutritional information
- Access food categories and detailed food items
- View featured content and promotions

### ❌ **Restrictions:**
- No admin panel access
- Cannot manage content (announcements, gallery)
- Cannot view other users' data
- Cannot modify system settings
- No access to system logs or analytics

---

## 🟡 **EDITOR ROLE** - Content Management
**Target Audience:** Content creators, marketing team, content moderators  
**Access Level:** Content management + limited admin access

### ✅ **Inherited Features:**
- **All User Role features** (food analysis, profile management, public content)

### ✅ **Additional Content Management Features:**

#### 📢 **Announcements & News Management**
- Create new announcements with rich content
- Edit existing announcements and news items
- Delete outdated or incorrect announcements
- Set announcement categories (feature, event, maintenance, update, other)
- Configure importance levels (low, medium, high)
- Schedule publish and expiry dates
- Preview announcements before publishing

#### 🖼️ **Gallery & Image Management**
- Upload images with metadata (title, description, alt text)
- Organize images by categories:
  - Food images
  - Recipe photos
  - Nutrition infographics
  - General content
  - Banner images
- Set featured images for homepage display
- Manage image display order
- Generate SEO-friendly slugs automatically
- Add tags for better organization
- Bulk image operations

#### 📊 **Limited Admin Panel Access**
- View content-focused dashboard statistics
- Access content management sections
- Monitor content-related activity logs
- View recent content changes and updates

### ❌ **Restrictions:**
- Cannot manage user accounts
- No access to system configuration
- Limited system logs access (content-related only)
- Cannot modify food database items
- No access to full system analytics

---

## 🔴 **ADMIN ROLE** - Full System Control
**Target Audience:** System administrators, technical team, business owners  
**Access Level:** Complete system access and control

### ✅ **Inherited Features:**
- **All Editor Role features** (content management, limited admin access)
- **All User Role features** (food analysis, profile management, public content)

### ✅ **Advanced Administrative Features:**

#### 👥 **User Management System**
- View all users with advanced filtering options:
  - Filter by role, status, registration date
  - Search by name, email, or ID
  - Sort by various criteria
- Create new user accounts with role assignment
- Edit user profiles, roles, and permissions
- Activate or deactivate user accounts
- Delete user accounts (with safety restrictions)
- Bulk user operations
- View user activity and login history

#### ⚙️ **System Configuration Management**
- **Site Settings:**
  - Site title and description
  - Contact information
  - Social media links
  - SEO meta tags
- **Feature Controls:**
  - Enable/disable user registration
  - Maintenance mode toggle
  - Feature flags and toggles
- **Security Settings:**
  - Upload file size limits
  - Allowed file types
  - Rate limiting configurations
  - IP whitelist management
- **API Configuration:**
  - Third-party service integrations
  - API rate limits
  - Authentication settings

#### 📊 **Advanced Analytics & Monitoring**
- **Dashboard Analytics:**
  - User registration trends
  - Food analysis activity
  - Content engagement metrics
  - System performance indicators
- **Visitor Tracking:**
  - Real-time online users
  - Daily/weekly visitor statistics
  - Page view analytics
  - User behavior patterns
- **System Health Monitoring:**
  - Server uptime and performance
  - Memory usage and optimization
  - Database performance metrics
  - Error rate monitoring

#### 📝 **Comprehensive System Logs**
- **Log Categories:**
  - Authentication events
  - User actions
  - Admin activities
  - System errors
  - Security events
  - API usage
- **Log Management:**
  - Advanced filtering and search
  - Export logs in JSON/CSV formats
  - Automated log cleanup
  - Real-time log monitoring
  - Alert system for critical events

#### 🍎 **Food Database Administration**
- Add new food items with complete nutritional data
- Edit existing food nutritional information
- Manage food categories and classifications
- Update portion sizes and serving information
- Import/export food database
- Validate nutritional data accuracy

#### 🛡️ **Security & System Administration**
- **Security Monitoring:**
  - Failed login attempt tracking
  - Suspicious activity detection
  - IP-based access control
  - Session management
- **System Maintenance:**
  - Database backup and restore
  - System updates and patches
  - Performance optimization
  - Cache management

---

## 🔐 **Access Control Summary**

### **Authentication Requirements:**
- **Public Endpoints:** No authentication required
- **User Endpoints:** Valid JWT token required
- **Editor Endpoints:** JWT token + Editor or Admin role
- **Admin Endpoints:** JWT token + Admin role specifically

### **Route Protection Levels:**
1. **Public Routes:** `/api/gallery`, `/api/announcements`, `/api/auth/*`
2. **Protected Routes:** `/api/users/*`, `/api/food-analysis/*`
3. **Editor Routes:** `/api/admin/gallery/*`, `/api/announcements/*` (management)
4. **Admin Routes:** `/api/admin/users/*`, `/api/admin/config/*`, `/api/admin/logs/*`

---

## 🎯 **Role Assignment Guidelines**

### **When to Assign USER Role:**
- Regular app users
- Customers tracking their nutrition
- General public accessing the app
- Trial or free-tier users

### **When to Assign EDITOR Role:**
- Content creators and writers
- Marketing team members
- Community managers
- Social media coordinators
- Content moderators

### **When to Assign ADMIN Role:**
- System administrators
- Technical team leads
- Business owners
- Senior management
- IT support staff

---

## 🔄 **Role Upgrade Path**

```
USER → EDITOR → ADMIN
```

**User to Editor:** When user needs content management capabilities  
**Editor to Admin:** When user needs full system administration access

**Note:** Role changes require Admin-level permissions and should be carefully considered based on business needs and security requirements.

---

**Last Updated:** June 1, 2025  
**Version:** 1.0.0  
**Status:** ✅ Complete & Implemented
