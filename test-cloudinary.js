const { v2: cloudinary } = require('cloudinary');

// Configure Cloudinary with environment variables
require('dotenv').config();

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dyytiuhzh',
  api_key: process.env.CLOUDINARY_API_KEY || '585677431321433',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'LfYKHUNmUyqHXvDZh-p8ILyHpos'
});

console.log('Cloudinary Config:');
console.log('Cloud Name:', process.env.CLOUDINARY_CLOUD_NAME || 'dyytiuhzh');
console.log('API Key:', process.env.CLOUDINARY_API_KEY || '585677431321433');
console.log('API Secret:', process.env.CLOUDINARY_API_SECRET ? '***hidden***' : 'LfYKHUNmUyqHXvDZh-p8ILyHpos');

// Test upload function
async function testCloudinaryUpload() {
  try {
    console.log('Testing Cloudinary connection...');
    
    // Test with a simple base64 image (1x1 pixel PNG)
    const testImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU8j6gAAAABJRU5ErkJggg==';
    
    const result = await cloudinary.uploader.upload(
      `data:image/png;base64,${testImageBase64}`,
      {
        resource_type: 'image',
        folder: 'nutrisnap-test'
      }
    );
    
    console.log('✅ Cloudinary upload successful!');
    console.log('Image URL:', result.secure_url);
    console.log('Public ID:', result.public_id);
    console.log('Format:', result.format);
    console.log('Size:', result.bytes, 'bytes');
    
    return result;
  } catch (error) {
    console.error('❌ Cloudinary upload failed:', error.message);
    return null;
  }
}

// Run the test
testCloudinaryUpload()
  .then(result => {
    if (result) {
      console.log('\n🎉 Cloudinary is working correctly!');
      process.exit(0);
    } else {
      console.log('\n💥 Cloudinary test failed!');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Test error:', error);
    process.exit(1);
  });
