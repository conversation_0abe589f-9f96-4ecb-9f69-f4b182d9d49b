/**
 * Utility functions for creating slugs and handling file names
 */

/**
 * Creates a URL-friendly slug from a string
 * @param {string} text - The text to slugify
 * @returns {string} - The slugified text
 */
function createSlug(text) {
  return text
    .toString()
    .toLowerCase()
    .trim()
    // Replace spaces with hyphens
    .replace(/\s+/g, '-')
    // Remove special characters except hyphens
    .replace(/[^\w\-]+/g, '')
    // Replace multiple hyphens with single hyphen
    .replace(/\-\-+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+/, '')
    .replace(/-+$/, '');
}

/**
 * Creates a unique filename using slug and file extension
 * @param {string} title - The title to slugify
 * @param {string} originalName - Original filename to extract extension
 * @param {string} [suffix] - Optional suffix to add for uniqueness
 * @returns {string} - The new filename
 */
function createSluggedFilename(title, originalName, suffix = '') {
  const slug = createSlug(title);
  const extension = originalName.split('.').pop().toLowerCase();
  
  // Add suffix if provided (for uniqueness)
  const finalSlug = suffix ? `${slug}-${suffix}` : slug;
  
  return `${finalSlug}.${extension}`;
}

/**
 * Creates a unique suffix using timestamp
 * @returns {string} - Timestamp-based suffix
 */
function createTimestampSuffix() {
  return Date.now().toString();
}

/**
 * Validates if a filename is safe
 * @param {string} filename - The filename to validate
 * @returns {boolean} - True if filename is safe
 */
function isValidFilename(filename) {
  // Check for valid characters and reasonable length
  const validPattern = /^[a-z0-9\-\.]+$/i;
  return validPattern.test(filename) && filename.length <= 255;
}

module.exports = {
  createSlug,
  createSluggedFilename,
  createTimestampSuffix,
  isValidFilename
};
