const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const UserSchema = new mongoose.Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  role: {
    type: String,
    enum: ['user', 'editor', 'admin'],
    default: 'user'
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended'],
    default: 'active'
  },
  lastLogin: {
    type: Date
  },
  preferences: {
    dietaryRestrictions: {
      type: [String],
      default: []
    },
    nutritionGoals: {
      calories: {
        type: Number,
        default: 2000
      },
      protein: {
        type: Number,
        default: 120
      },
      carbs: {
        type: Number,
        default: 250
      },
      fat: {
        type: Number,
        default: 70
      }
    },
    units: {
      type: String,
      enum: ['metric', 'imperial'],
      default: 'metric'
    },
    notifications: {
      mealReminders: {
        type: Boolean,
        default: true
      },
      weeklyReports: {
        type: Boolean,
        default: true
      },
      achievementAlerts: {
        type: Boolean,
        default: true
      },
      emailNotifications: {
        type: Boolean,
        default: true
      }
    }
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Hash password before saving
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    return next();
  }
  
  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function(candidatePassword) {
  return await bcrypt.compare(candidatePassword, this.password);
};

// Method to check if user has admin role
UserSchema.methods.isAdminRole = function() {
  return this.role === 'admin' || this.isAdmin;
};

// Method to check if user has editor role or higher
UserSchema.methods.isEditorRole = function() {
  return this.role === 'editor' || this.role === 'admin' || this.isAdmin;
};

// Method to check if user can access admin panel
UserSchema.methods.canAccessAdminPanel = function() {
  return this.status === 'active' && (this.role === 'admin' || this.role === 'editor' || this.isAdmin);
};

// Update last login
UserSchema.methods.updateLastLogin = function() {
  this.lastLogin = new Date();
  return this.save();
};

module.exports = mongoose.model('User', UserSchema);
