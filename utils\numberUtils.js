/**
 * Number utility functions for handling floating-point precision issues
 */

/**
 * Round a number to specified decimal places
 * @param {number} num - The number to round
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {number} - Properly rounded number
 */
function roundToDecimals(num, decimals = 1) {
  if (typeof num !== 'number' || isNaN(num)) {
    return 0;
  }
  
  const factor = Math.pow(10, decimals);
  return Math.round((num + Number.EPSILON) * factor) / factor;
}

/**
 * Round nutritional values consistently
 * @param {number} value - The nutritional value to round
 * @returns {number} - Rounded value (1 decimal place for precision)
 */
function roundNutritionalValue(value) {
  return roundToDecimals(value, 1);
}

/**
 * Round calorie values (typically whole numbers)
 * @param {number} calories - The calorie value to round
 * @returns {number} - Rounded calories (whole number)
 */
function roundCalories(calories) {
  return Math.round(calories || 0);
}

/**
 * Format nutritional value for display
 * @param {number} value - The value to format
 * @param {number} decimals - Number of decimal places (default: 1)
 * @returns {string} - Formatted string
 */
function formatNutritionalValue(value, decimals = 1) {
  const rounded = roundToDecimals(value, decimals);
  
  // Remove trailing zeros for cleaner display
  if (decimals > 0) {
    return parseFloat(rounded.toFixed(decimals)).toString();
  }
  
  return rounded.toString();
}

/**
 * Parse and clean nutritional value from AI response
 * @param {string|number} value - Raw value from AI (may contain text)
 * @returns {number} - Cleaned and parsed number
 */
function parseNutritionalValue(value) {
  if (typeof value === 'number') {
    return value;
  }

  if (typeof value === 'string') {
    // Extract all numbers from the string and take the largest one
    // This handles cases like "Approx. 950 kcal" or "20 g"
    const numbers = value.match(/\d+\.?\d*/g);
    if (numbers && numbers.length > 0) {
      // Convert to numbers and find the largest (most likely the main value)
      const numericValues = numbers.map(num => parseFloat(num)).filter(num => !isNaN(num));
      if (numericValues.length > 0) {
        return Math.max(...numericValues);
      }
    }
    return 0;
  }

  return 0;
}

/**
 * Round all nutritional values in an object
 * @param {object} nutrition - Object containing nutritional values
 * @returns {object} - Object with rounded values
 */
function roundNutritionObject(nutrition) {
  const rounded = {};
  
  for (const [key, value] of Object.entries(nutrition)) {
    if (key.toLowerCase().includes('calorie')) {
      rounded[key] = roundCalories(value);
    } else {
      rounded[key] = roundNutritionalValue(value);
    }
  }
  
  return rounded;
}

module.exports = {
  roundToDecimals,
  roundNutritionalValue,
  roundCalories,
  formatNutritionalValue,
  parseNutritionalValue,
  roundNutritionObject
};
