{"name": "nutrisnap-backend", "version": "1.0.0", "description": "Backend for NutriSnap food analysis app", "main": "server.js", "scripts": {"dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon"}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "cors": "^2.8.5", "csurf": "^1.11.0", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}