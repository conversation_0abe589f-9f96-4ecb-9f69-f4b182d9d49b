const express = require('express');
const router = express.Router();
const { protect, admin } = require('../middleware/auth');
const FoodDatabase = require('../models/FoodDatabase');

// @route   GET /api/food-database
// @desc    Get all food items or search by name
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const { search, category, limit = 20, page = 1 } = req.query;
    
    // Build query
    let query = {};
    
    // Add search filter if provided
    if (search) {
      query.$text = { $search: search };
    }
    
    // Add category filter if provided
    if (category) {
      query.category = category;
    }
    
    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Find food items
    const foodItems = await FoodDatabase.find(query)
      .limit(parseInt(limit))
      .skip(skip)
      .sort({ name: 1 });
    
    // Count total documents
    const total = await FoodDatabase.countDocuments(query);
    
    res.json({
      success: true,
      count: foodItems.length,
      total,
      pages: Math.ceil(total / parseInt(limit)),
      page: parseInt(page),
      foodItems
    });
  } catch (error) {
    console.error('Error in get food items:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/food-database/categories
// @desc    Get all food categories
// @access  Private
router.get('/categories', protect, async (req, res) => {
  try {
    // Get distinct categories
    const categories = await FoodDatabase.distinct('category');
    
    res.json({
      success: true,
      categories
    });
  } catch (error) {
    console.error('Error in get food categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/food-database/:id
// @desc    Get food item by ID
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    // Find food item by ID
    const foodItem = await FoodDatabase.findById(req.params.id);
    
    if (!foodItem) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }
    
    res.json({
      success: true,
      foodItem
    });
  } catch (error) {
    console.error('Error in get food item by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/food-database
// @desc    Create a new food item
// @access  Private (Admin only)
router.post('/', [protect, admin], async (req, res) => {
  try {
    const { name, category, nutrientsPerServing, commonPortions } = req.body;
    
    // Check if food item already exists
    const existingFoodItem = await FoodDatabase.findOne({ name });
    
    if (existingFoodItem) {
      return res.status(400).json({
        success: false,
        message: 'Food item already exists'
      });
    }
    
    // Create new food item
    const foodItem = new FoodDatabase({
      name,
      category,
      nutrientsPerServing,
      commonPortions
    });
    
    // Save food item to database
    await foodItem.save();
    
    res.status(201).json({
      success: true,
      foodItem
    });
  } catch (error) {
    console.error('Error in create food item:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/food-database/:id
// @desc    Update a food item
// @access  Private (Admin only)
router.put('/:id', [protect, admin], async (req, res) => {
  try {
    const { name, category, nutrientsPerServing, commonPortions } = req.body;
    
    // Find food item by ID
    let foodItem = await FoodDatabase.findById(req.params.id);
    
    if (!foodItem) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }
    
    // Update fields
    if (name) foodItem.name = name;
    if (category) foodItem.category = category;
    if (nutrientsPerServing) foodItem.nutrientsPerServing = nutrientsPerServing;
    if (commonPortions) foodItem.commonPortions = commonPortions;
    
    // Save updated food item
    await foodItem.save();
    
    res.json({
      success: true,
      foodItem
    });
  } catch (error) {
    console.error('Error in update food item:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/food-database/:id
// @desc    Delete a food item
// @access  Private (Admin only)
router.delete('/:id', [protect, admin], async (req, res) => {
  try {
    // Find food item by ID
    const foodItem = await FoodDatabase.findById(req.params.id);
    
    if (!foodItem) {
      return res.status(404).json({
        success: false,
        message: 'Food item not found'
      });
    }
    
    // Delete food item
    await foodItem.remove();
    
    res.json({
      success: true,
      message: 'Food item deleted'
    });
  } catch (error) {
    console.error('Error in delete food item:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
