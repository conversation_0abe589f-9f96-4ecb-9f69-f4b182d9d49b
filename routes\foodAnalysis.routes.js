const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect } = require('../middleware/auth');
const upload = require('../middleware/upload');
const multer = require('multer');
const FoodAnalysis = require('../models/FoodAnalysis');
const { slugify } = require('../utils/slugify');
const { uploadImage } = require('../config/cloudinary');
const GeminiService = require('../services/geminiService');
const rateLimit = require('express-rate-limit');

// Initialize Gemini service
const geminiService = new GeminiService();

// Rate limiting for AI analysis
const analysisRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each user to 10 analysis requests per windowMs
  message: {
    success: false,
    message: 'Too many analysis requests. Please try again later.',
    retryAfter: '15 minutes'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Configure multer for AI analysis (memory storage)
const aiAnalysisUpload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
    files: 1
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif'];
      if (supportedFormats.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Unsupported image format. Please use JPEG, PNG, WebP, or AVIF.'), false);
      }
    } else {
      cb(new Error('Only image files are allowed.'), false);
    }
  }
});

// @route   POST /api/food-analysis/analyze
// @desc    Analyze food image using AI (Gemini)
// @access  Private
router.post('/analyze', protect, analysisRateLimit, aiAnalysisUpload.single('image'), async (req, res) => {
  try {
    // Check if image was uploaded
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No image file provided. Please upload an image.'
      });
    }

    const { buffer, mimetype, originalname, size } = req.file;

    console.log(`Processing food analysis for user ${req.user._id}:`);
    console.log(`- File: ${originalname}`);
    console.log(`- Size: ${(size / 1024 / 1024).toFixed(2)}MB`);
    console.log(`- Type: ${mimetype}`);

    // Analyze the image using Gemini
    const analysisResult = await geminiService.analyzeFoodImage(buffer, mimetype);

    if (!analysisResult.success) {
      return res.status(400).json({
        success: false,
        message: analysisResult.message,
        error: analysisResult.error
      });
    }

    // Log successful analysis
    console.log(`✅ Analysis completed for user ${req.user._id}`);
    console.log(`- Food items found: ${analysisResult.data.food_items?.length || 0}`);

    // Return the analysis result
    res.json({
      success: true,
      message: 'Food analysis completed successfully',
      data: {
        analysis: analysisResult.data,
        metadata: {
          filename: originalname,
          fileSize: size,
          mimeType: mimetype,
          analyzedAt: new Date().toISOString(),
          userId: req.user._id
        }
      }
    });

  } catch (error) {
    console.error('Food analysis error:', error);

    // Handle multer errors
    if (error instanceof multer.MulterError) {
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({
          success: false,
          message: 'File too large. Maximum size is 10MB.'
        });
      }
      return res.status(400).json({
        success: false,
        message: `Upload error: ${error.message}`
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during food analysis',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// @route   POST /api/food-analysis/upload
// @desc    Upload food image and create analysis
// @access  Private
router.post(
  '/upload',
  protect,
  upload.single('image'),
  [
    check('mealCategory', 'Meal category is required').not().isEmpty(),
    check('mealDateTime', 'Meal date and time is required').not().isEmpty(),
    check('recognitionResults', 'Recognition results are required').isArray(),
    check('nutritionalSummary', 'Nutritional summary is required').isObject()
  ],
  async (req, res) => {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: 'Please upload an image'
        });
      }

      // Create slug for food analysis
      const mealCategory = req.body.mealCategory || 'meal';
      const timestamp = Date.now();
      const baseSlug = slugify(`${mealCategory}-${timestamp}`);

      // Upload to Cloudinary
      const cloudinaryResult = await uploadImage(req.file.buffer, baseSlug, 'nutrisnap/food-analysis');

      if (!cloudinaryResult.success) {
        return res.status(500).json({
          success: false,
          message: 'Failed to upload image to cloud storage'
        });
      }

      // Create new food analysis
      const foodAnalysis = new FoodAnalysis({
        userId: req.user._id,
        imageUrl: cloudinaryResult.url,
        mealCategory: req.body.mealCategory,
        mealDateTime: req.body.mealDateTime,
        userNotes: req.body.userNotes || '',
        recognitionResults: JSON.parse(req.body.recognitionResults),
        nutritionalSummary: JSON.parse(req.body.nutritionalSummary)
      });

      // Save food analysis to database
      await foodAnalysis.save();

      res.status(201).json({
        success: true,
        foodAnalysis
      });
    } catch (error) {
      console.error('Error in upload food analysis:', error);
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   GET /api/food-analysis
// @desc    Get all food analyses for a user
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    // Get query parameters for filtering
    const { startDate, endDate, mealCategory } = req.query;
    
    // Build query
    const query = { userId: req.user._id };
    
    // Add date range filter if provided
    if (startDate && endDate) {
      query.mealDateTime = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    }
    
    // Add meal category filter if provided
    if (mealCategory) {
      query.mealCategory = mealCategory;
    }
    
    // Find food analyses for user
    const foodAnalyses = await FoodAnalysis.find(query).sort({ mealDateTime: -1 });

    res.json({
      success: true,
      count: foodAnalyses.length,
      foodAnalyses
    });
  } catch (error) {
    console.error('Error in get food analyses:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/food-analysis/supported-formats
// @desc    Get list of supported image formats
// @access  Public
router.get('/supported-formats', (req, res) => {
  res.json({
    success: true,
    data: {
      formats: [
        {
          extension: '.jpg',
          mimeType: 'image/jpeg',
          description: 'JPEG image format'
        },
        {
          extension: '.jpeg',
          mimeType: 'image/jpeg',
          description: 'JPEG image format'
        },
        {
          extension: '.png',
          mimeType: 'image/png',
          description: 'PNG image format'
        },
        {
          extension: '.webp',
          mimeType: 'image/webp',
          description: 'WebP image format'
        },
        {
          extension: '.avif',
          mimeType: 'image/avif',
          description: 'AVIF image format'
        }
      ],
      maxFileSize: '10MB',
      recommendations: [
        'Use JPEG for photos with many colors',
        'Use PNG for images with transparency',
        'Use WebP for better compression',
        'Ensure good lighting for better analysis',
        'Include the entire meal in the frame'
      ]
    }
  });
});

// @route   GET /api/food-analysis/:id
// @desc    Get food analysis by ID
// @access  Private
router.get('/:id', protect, async (req, res) => {
  try {
    // Find food analysis by ID
    const foodAnalysis = await FoodAnalysis.findById(req.params.id);

    if (!foodAnalysis) {
      return res.status(404).json({
        success: false,
        message: 'Food analysis not found'
      });
    }

    // Check if user owns the food analysis
    if (foodAnalysis.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to access this food analysis'
      });
    }

    res.json({
      success: true,
      foodAnalysis
    });
  } catch (error) {
    console.error('Error in get food analysis by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/food-analysis/:id
// @desc    Update food analysis
// @access  Private
router.put('/:id', protect, async (req, res) => {
  try {
    // Find food analysis by ID
    let foodAnalysis = await FoodAnalysis.findById(req.params.id);

    if (!foodAnalysis) {
      return res.status(404).json({
        success: false,
        message: 'Food analysis not found'
      });
    }

    // Check if user owns the food analysis
    if (foodAnalysis.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this food analysis'
      });
    }

    // Update fields
    const { mealCategory, mealDateTime, userNotes, recognitionResults, nutritionalSummary } = req.body;

    if (mealCategory) foodAnalysis.mealCategory = mealCategory;
    if (mealDateTime) foodAnalysis.mealDateTime = mealDateTime;
    if (userNotes !== undefined) foodAnalysis.userNotes = userNotes;
    if (recognitionResults) foodAnalysis.recognitionResults = recognitionResults;
    if (nutritionalSummary) foodAnalysis.nutritionalSummary = nutritionalSummary;

    // Save updated food analysis
    await foodAnalysis.save();

    res.json({
      success: true,
      foodAnalysis
    });
  } catch (error) {
    console.error('Error in update food analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/food-analysis/:id
// @desc    Delete food analysis
// @access  Private
router.delete('/:id', protect, async (req, res) => {
  try {
    // Find food analysis by ID
    const foodAnalysis = await FoodAnalysis.findById(req.params.id);

    if (!foodAnalysis) {
      return res.status(404).json({
        success: false,
        message: 'Food analysis not found'
      });
    }

    // Check if user owns the food analysis
    if (foodAnalysis.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this food analysis'
      });
    }

    // Delete food analysis
    await foodAnalysis.remove();

    res.json({
      success: true,
      message: 'Food analysis deleted'
    });
  } catch (error) {
    console.error('Error in delete food analysis:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/food-analysis/test-gemini
// @desc    Test Gemini API connection
// @access  Private (Admin only)
router.post('/test-gemini', protect, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'Admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Admin privileges required.'
      });
    }

    const testResult = await geminiService.testConnection();

    res.json({
      success: testResult.success,
      message: testResult.success ? 'Gemini API connection successful' : 'Gemini API connection failed',
      data: testResult
    });

  } catch (error) {
    console.error('API test error:', error);
    res.status(500).json({
      success: false,
      message: 'Error testing API connection',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
