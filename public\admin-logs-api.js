/**
 * Admin Logs API Integration Module
 * 
 * This module provides functions to interact with the admin logs API
 * and can be used in any frontend framework (React, Vue, Angular, etc.)
 */

class AdminLogsAPI {
    constructor(baseURL = 'http://localhost:5000/api', getToken = null) {
        this.baseURL = baseURL;
        this.getToken = getToken || (() => localStorage.getItem('authToken'));
    }

    /**
     * Get authorization headers
     */
    getHeaders() {
        const token = this.getToken();
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
    }

    /**
     * Fetch log statistics
     * @param {number} days - Number of days for detailed stats (default: 7)
     * @returns {Promise<Object>} Log statistics
     */
    async getLogStats(days = 7) {
        try {
            const response = await fetch(`${this.baseURL}/admin/logs/stats?days=${days}`, {
                headers: this.getHeaders()
            });

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch log statistics');
            }

            return data.data;
        } catch (error) {
            console.error('Error fetching log stats:', error);
            throw error;
        }
    }

    /**
     * Fetch logs with filtering and pagination
     * @param {Object} options - Query options
     * @param {number} options.page - Page number (default: 1)
     * @param {number} options.limit - Items per page (default: 20)
     * @param {string} options.level - Log level filter
     * @param {string} options.category - Category filter
     * @param {string} options.search - Search term
     * @param {string} options.startDate - Start date filter
     * @param {string} options.endDate - End date filter
     * @param {string} options.userId - User ID filter
     * @returns {Promise<Object>} Logs and pagination info
     */
    async getLogs(options = {}) {
        try {
            const {
                page = 1,
                limit = 20,
                level = '',
                category = '',
                search = '',
                startDate = '',
                endDate = '',
                userId = ''
            } = options;

            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString()
            });

            if (level) params.append('level', level);
            if (category) params.append('category', category);
            if (search) params.append('search', search);
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);
            if (userId) params.append('userId', userId);

            const response = await fetch(`${this.baseURL}/admin/logs?${params}`, {
                headers: this.getHeaders()
            });

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch logs');
            }

            return data.data;
        } catch (error) {
            console.error('Error fetching logs:', error);
            throw error;
        }
    }

    /**
     * Get recent logs
     * @param {number} limit - Number of recent logs to fetch (default: 100)
     * @param {string} level - Log level filter
     * @param {string} category - Category filter
     * @returns {Promise<Array>} Recent logs
     */
    async getRecentLogs(limit = 100, level = '', category = '') {
        try {
            const params = new URLSearchParams({ limit: limit.toString() });
            if (level) params.append('level', level);
            if (category) params.append('category', category);

            const response = await fetch(`${this.baseURL}/admin/logs/recent?${params}`, {
                headers: this.getHeaders()
            });

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch recent logs');
            }

            return data.data.logs;
        } catch (error) {
            console.error('Error fetching recent logs:', error);
            throw error;
        }
    }

    /**
     * Get specific log by ID
     * @param {string} logId - Log ID
     * @returns {Promise<Object>} Log details
     */
    async getLogById(logId) {
        try {
            const response = await fetch(`${this.baseURL}/admin/logs/${logId}`, {
                headers: this.getHeaders()
            });

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to fetch log details');
            }

            return data.data.log;
        } catch (error) {
            console.error('Error fetching log details:', error);
            throw error;
        }
    }

    /**
     * Export logs
     * @param {string} format - Export format ('json' or 'csv')
     * @returns {Promise<Blob>} Export file blob
     */
    async exportLogs(format = 'json') {
        try {
            const response = await fetch(`${this.baseURL}/admin/logs/export?format=${format}`, {
                headers: this.getHeaders()
            });

            if (!response.ok) {
                throw new Error('Failed to export logs');
            }

            return await response.blob();
        } catch (error) {
            console.error('Error exporting logs:', error);
            throw error;
        }
    }

    /**
     * Clean up old logs
     * @param {number} days - Delete logs older than this many days (default: 30)
     * @param {string} level - Only delete logs of this level (optional)
     * @returns {Promise<Object>} Cleanup result
     */
    async cleanupLogs(days = 30, level = '') {
        try {
            const body = { days };
            if (level) body.level = level;

            const response = await fetch(`${this.baseURL}/admin/logs/cleanup`, {
                method: 'DELETE',
                headers: this.getHeaders(),
                body: JSON.stringify(body)
            });

            const data = await response.json();
            
            if (!data.success) {
                throw new Error(data.message || 'Failed to cleanup logs');
            }

            return data.data;
        } catch (error) {
            console.error('Error cleaning up logs:', error);
            throw error;
        }
    }
}

/**
 * Utility functions for formatting log data
 */
const LogUtils = {
    /**
     * Format log level with appropriate styling
     * @param {string} level - Log level
     * @returns {Object} Formatted level info
     */
    formatLevel(level) {
        const levelConfig = {
            info: { color: '#27ae60', background: '#d5f4e6' },
            warning: { color: '#f39c12', background: '#fef9e7' },
            error: { color: '#e74c3c', background: '#fadbd8' },
            debug: { color: '#3498db', background: '#eaf2f8' },
            critical: { color: '#c0392b', background: '#f4cccc' }
        };

        return {
            level: level.toUpperCase(),
            ...levelConfig[level] || levelConfig.info
        };
    },

    /**
     * Format date for display
     * @param {string|Date} date - Date to format
     * @returns {string} Formatted date
     */
    formatDate(date) {
        return new Date(date).toLocaleString();
    },

    /**
     * Format user info
     * @param {Object} user - User object
     * @returns {string} Formatted user name
     */
    formatUser(user) {
        if (!user) return 'System';
        return `${user.firstName} ${user.lastName}`.trim() || user.email || 'Unknown User';
    },

    /**
     * Get level icon
     * @param {string} level - Log level
     * @returns {string} Icon character or emoji
     */
    getLevelIcon(level) {
        const icons = {
            info: 'ℹ️',
            warning: '⚠️',
            error: '❌',
            debug: '🔧',
            critical: '🚨'
        };
        return icons[level] || 'ℹ️';
    }
};

/**
 * React Hook for using logs API (if using React)
 */
function useAdminLogs(apiInstance) {
    const [stats, setStats] = React.useState(null);
    const [logs, setLogs] = React.useState([]);
    const [pagination, setPagination] = React.useState(null);
    const [loading, setLoading] = React.useState(false);
    const [error, setError] = React.useState(null);

    const fetchStats = React.useCallback(async () => {
        try {
            setLoading(true);
            const statsData = await apiInstance.getLogStats();
            setStats(statsData);
            setError(null);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [apiInstance]);

    const fetchLogs = React.useCallback(async (options = {}) => {
        try {
            setLoading(true);
            const logsData = await apiInstance.getLogs(options);
            setLogs(logsData.logs);
            setPagination(logsData.pagination);
            setError(null);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [apiInstance]);

    return {
        stats,
        logs,
        pagination,
        loading,
        error,
        fetchStats,
        fetchLogs
    };
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    // Node.js/CommonJS
    module.exports = { AdminLogsAPI, LogUtils, useAdminLogs };
} else if (typeof window !== 'undefined') {
    // Browser global
    window.AdminLogsAPI = AdminLogsAPI;
    window.LogUtils = LogUtils;
    if (typeof React !== 'undefined') {
        window.useAdminLogs = useAdminLogs;
    }
}

// Example usage:
/*
// Initialize the API
const logsAPI = new AdminLogsAPI('http://localhost:5000/api', () => localStorage.getItem('token'));

// Fetch and display stats
async function displayLogStats() {
    try {
        const stats = await logsAPI.getLogStats();
        console.log('Total Logs:', stats.summary.totalLogs);
        console.log('Errors:', stats.summary.errors);
        console.log('Warnings:', stats.summary.warnings);
        console.log('Info:', stats.summary.info);
    } catch (error) {
        console.error('Failed to load stats:', error);
    }
}

// Fetch logs with filters
async function displayLogs() {
    try {
        const data = await logsAPI.getLogs({
            page: 1,
            limit: 20,
            level: 'error',
            category: 'auth'
        });
        
        console.log('Logs:', data.logs);
        console.log('Pagination:', data.pagination);
    } catch (error) {
        console.error('Failed to load logs:', error);
    }
}
*/
