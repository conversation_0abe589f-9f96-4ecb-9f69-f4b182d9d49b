# NutriSnap Backend

This is the backend API for the NutriSnap food analysis application. It provides endpoints for user authentication, food analysis, food database, and announcements.

## Technologies Used

- Node.js
- Express.js
- MongoDB with Mongoose
- JWT for authentication
- Multer for file uploads
- bcryptjs for password hashing

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MongoDB (local or Atlas)

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file in the root directory with the following variables:
   ```
   PORT=5000
   MONGODB_URI=mongodb://localhost:27017/nutrisnap
   JWT_SECRET=your_jwt_secret_key_here
   SESSION_SECRET=your_session_secret_key_here
   NODE_ENV=development

   # Optional: Admin IP whitelist (comma-separated)
   ADMIN_ALLOWED_IPS=127.0.0.1,::1
   ```

### Running the Server

```
npm run dev
```

### Seeding the Database

To seed the database with initial data:

```
node seed.js
```

## API Endpoints

### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login and get token

### Users

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/preferences` - Update user preferences

### Food Analysis

- `POST /api/food-analysis/upload` - Upload food image and create analysis
- `GET /api/food-analysis` - Get all food analyses for a user
- `GET /api/food-analysis/:id` - Get food analysis by ID
- `PUT /api/food-analysis/:id` - Update food analysis
- `DELETE /api/food-analysis/:id` - Delete food analysis

### Food Database

- `GET /api/food-database` - Get all food items or search by name
- `GET /api/food-database/categories` - Get all food categories
- `GET /api/food-database/:id` - Get food item by ID
- `POST /api/food-database` - Create a new food item (Admin only)
- `PUT /api/food-database/:id` - Update a food item (Admin only)
- `DELETE /api/food-database/:id` - Delete a food item (Admin only)

### Announcements

- `GET /api/announcements` - Get all active announcements
- `GET /api/announcements/all` - Get all announcements (Admin only)
- `GET /api/announcements/:id` - Get announcement by ID (Admin only)
- `POST /api/announcements` - Create a new announcement (Admin only)
- `PUT /api/announcements/:id` - Update an announcement (Admin only)
- `DELETE /api/announcements/:id` - Delete an announcement (Admin only)

### Gallery

- `GET /api/gallery` - Get active gallery items (Public)
- `GET /api/gallery/featured` - Get featured gallery items (Public)
- `GET /api/gallery/categories` - Get gallery categories (Public)
- `GET /api/gallery/:slug` - Get gallery item by slug (Public)

### Admin Panel

#### Dashboard
- `GET /api/admin/dashboard` - Get admin dashboard statistics
- `GET /api/admin/analytics` - Get detailed analytics data
- `GET /api/admin/system-info` - Get system information (Admin only)

#### User Management
- `GET /api/admin/users` - Get all users with pagination and filtering
- `GET /api/admin/users/:id` - Get user by ID
- `POST /api/admin/users` - Create a new user (Admin only)
- `PUT /api/admin/users/:id` - Update user (Admin only)
- `DELETE /api/admin/users/:id` - Delete user (Admin only)

#### Gallery Management
- `GET /api/admin/gallery` - Get all gallery items with pagination
- `GET /api/admin/gallery/:id` - Get gallery item by ID
- `POST /api/admin/gallery` - Upload new gallery item (Editor/Admin)
- `PUT /api/admin/gallery/:id` - Update gallery item (Editor/Admin)
- `DELETE /api/admin/gallery/:id` - Delete gallery item (Editor/Admin)

#### System Configuration
- `GET /api/admin/config` - Get all system configurations (Admin only)
- `GET /api/admin/config/public` - Get public configurations
- `GET /api/admin/config/:key` - Get specific configuration (Admin only)
- `POST /api/admin/config` - Create/update configuration (Admin only)
- `PUT /api/admin/config/:key` - Update specific configuration (Admin only)
- `DELETE /api/admin/config/:key` - Delete configuration (Admin only)

#### System Logs
- `GET /api/admin/logs` - Get system logs with filtering (Admin only)
- `GET /api/admin/logs/stats` - Get log statistics (Admin only)
- `GET /api/admin/logs/recent` - Get recent logs (Admin only)
- `GET /api/admin/logs/:id` - Get specific log entry (Admin only)
- `DELETE /api/admin/logs/cleanup` - Clean up old logs (Admin only)
- `GET /api/admin/logs/export` - Export logs as JSON/CSV (Admin only)

## Project Structure

```
nutrisnap-backend/
├── middleware/         # Middleware functions
│   ├── auth.js         # Authentication middleware
│   └── upload.js       # File upload middleware
├── models/             # Mongoose models
│   ├── User.js
│   ├── FoodAnalysis.js
│   ├── FoodDatabase.js
│   └── Announcement.js
├── routes/             # API routes
│   ├── auth.routes.js
│   ├── user.routes.js
│   ├── foodAnalysis.routes.js
│   ├── foodDatabase.routes.js
│   └── announcements.routes.js
├── utils/              # Utility functions
│   └── generateToken.js
├── uploads/            # Uploaded files
├── .env                # Environment variables
├── package.json
├── seed.js             # Database seeding script
└── server.js           # Entry point
```

## Authentication & User Roles

The API uses JWT (JSON Web Tokens) for authentication. To access protected routes, include the token in the Authorization header:

```
Authorization: Bearer <token>
```

### User Roles

The system supports three user roles with different permission levels:

| Role | Permissions |
|------|-------------|
| **User** | - View content only<br>- Access food analysis features<br>- Manage personal profile and preferences |
| **Editor** | - All User permissions<br>- Manage announcements (CRUD)<br>- Manage gallery images (CRUD)<br>- Access admin panel (limited) |
| **Admin** | - All Editor permissions<br>- Manage users (CRUD)<br>- Manage system configurations<br>- View system logs and analytics<br>- Full admin panel access |

### Role-Based Access Control

- **Public routes**: No authentication required
- **Protected routes**: Require valid JWT token
- **Admin Panel routes**: Require Editor or Admin role
- **Admin-only routes**: Require Admin role specifically

### Security Features

- **Rate Limiting**: API endpoints are rate-limited to prevent abuse
- **CSRF Protection**: State-changing requests require CSRF tokens
- **Security Headers**: Helmet.js provides security headers
- **Session Management**: Secure session handling with configurable options
- **Input Validation**: All inputs are validated and sanitized
- **System Logging**: All admin actions and security events are logged

## Error Handling

All endpoints return a consistent error format:

```json
{
  "success": false,
  "message": "Error message",
  "errors": [] // Optional validation errors
}
```

## Success Responses

All endpoints return a consistent success format:

```json
{
  "success": true,
  "data": {} // Response data
}
```
