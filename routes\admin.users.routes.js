const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const { protect, adminPanel, admin, requireRole } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/security');
const User = require('../models/User');
const SystemLog = require('../models/SystemLog');

// Apply middleware to all routes - RATE LIMITING TEMPORARILY DISABLED
// router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/users
// @desc    Get all users with pagination and filtering
// @access  Private (Admin Panel Access)
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      role = '',
      isActive = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (role) {
      query.role = role;
    }
    
    // Handle both isActive and status parameters for backward compatibility
    if (isActive !== '') {
      // Convert old isActive parameter to status
      query.status = isActive === 'true' ? 'active' : 'inactive';
    } else if (status !== '') {
      // Use status directly
      query.status = status;
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get users
    const users = await User.find(query)
      .select('-password')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Transform users to include id field for frontend compatibility
    const transformedUsers = users.map(user => {
      const userObj = user.toObject();
      userObj.id = userObj._id; // Add id field for frontend compatibility
      return userObj;
    });

    // Get total count for pagination
    const total = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users: transformedUsers,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error in get users:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/users/:id
// @desc    Get user by ID
// @access  Private (Admin Panel Access)
router.get('/:id', async (req, res) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Transform user to include id field for frontend compatibility
    const userObj = user.toObject();
    userObj.id = userObj._id; // Add id field for frontend compatibility

    res.json({
      success: true,
      data: { user: userObj }
    });

  } catch (error) {
    console.error('Error in get user by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/admin/users
// @desc    Create a new user
// @access  Private (Admin Only)
router.post(
  '/',
  requireRole(['admin']),
  [
    check('email', 'Please include a valid email').isEmail(),
    check('password', 'Password must be at least 6 characters').isLength({ min: 6 }),
    check('firstName', 'First name is required').not().isEmpty(),
    check('lastName', 'Last name is required').not().isEmpty(),
    check('role', 'Role must be user, editor, or admin').isIn(['user', 'editor', 'admin']),
    check('status', 'Status must be active, inactive, or suspended').optional().isIn(['active', 'inactive', 'suspended'])
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      const { email, password, firstName, lastName, role, isActive, status = 'active' } = req.body;

      // Handle backward compatibility with isActive parameter
      let userStatus = status;
      if (isActive !== undefined) {
        userStatus = isActive ? 'active' : 'inactive';
      }

      // Check if user already exists
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email already exists'
        });
      }

      // Create new user
      const user = new User({
        email,
        password,
        firstName,
        lastName,
        role,
        status: userStatus,
        isAdmin: role === 'admin'
      });

      await user.save();

      // Log admin action
      await SystemLog.logAdmin(
        'user_created',
        req.user._id,
        req.user.email,
        req.ip,
        {
          targetUserId: user._id,
          targetUserEmail: user.email,
          role: user.role
        }
      );

      // Return user without password
      const userResponse = user.toObject();
      delete userResponse.password;
      userResponse.id = userResponse._id; // Add id field for frontend compatibility

      res.status(201).json({
        success: true,
        data: { user: userResponse }
      });

    } catch (error) {
      console.error('Error in create user:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   PUT /api/admin/users/:id
// @desc    Update user
// @access  Private (Admin Only)
router.put(
  '/:id',
  requireRole(['admin']),
  [
    check('email', 'Please include a valid email').optional().isEmail(),
    check('firstName', 'First name cannot be empty').optional().not().isEmpty(),
    check('lastName', 'Last name cannot be empty').optional().not().isEmpty(),
    check('role', 'Role must be user, editor, or admin').optional().isIn(['user', 'editor', 'admin']),
    check('status', 'Status must be active, inactive, or suspended').optional().isIn(['active', 'inactive', 'suspended'])
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    try {
      const { email, firstName, lastName, role, isActive, status } = req.body;

      // Handle backward compatibility with isActive parameter
      let userStatus = status;
      if (isActive !== undefined) {
        userStatus = isActive ? 'active' : 'inactive';
      }

      // Find user
      const user = await User.findById(req.params.id);
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Prevent admin from deactivating themselves
      if (req.user._id.toString() === user._id.toString() && userStatus && userStatus !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'You cannot deactivate your own account'
        });
      }

      // Check if email is already taken by another user
      if (email && email !== user.email) {
        const existingUser = await User.findOne({ email });
        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: 'Email is already taken'
          });
        }
      }

      // Store old values for logging
      const oldValues = {
        email: user.email,
        role: user.role,
        status: user.status
      };

      // Update fields
      if (email) user.email = email;
      if (firstName) user.firstName = firstName;
      if (lastName) user.lastName = lastName;
      if (role) {
        user.role = role;
        user.isAdmin = role === 'admin';
      }
      if (userStatus) user.status = userStatus;

      await user.save();

      // Log admin action
      await SystemLog.logAdmin(
        'user_updated',
        req.user._id,
        req.user.email,
        req.ip,
        {
          targetUserId: user._id,
          targetUserEmail: user.email,
          oldValues,
          newValues: {
            email: user.email,
            role: user.role,
            status: user.status
          }
        }
      );

      // Return user without password
      const userResponse = user.toObject();
      delete userResponse.password;
      userResponse.id = userResponse._id; // Add id field for frontend compatibility

      res.json({
        success: true,
        data: { user: userResponse }
      });

    } catch (error) {
      console.error('Error in update user:', error);
      await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
      
      res.status(500).json({
        success: false,
        message: 'Server error'
      });
    }
  }
);

// @route   DELETE /api/admin/users/:id
// @desc    Delete user
// @access  Private (Admin Only)
router.delete('/:id', requireRole(['admin']), async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Prevent admin from deleting themselves
    if (req.user._id.toString() === user._id.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot delete your own account'
      });
    }

    // Store user info for logging before deletion
    const deletedUserInfo = {
      id: user._id,
      email: user.email,
      role: user.role
    };

    await User.findByIdAndDelete(req.params.id);

    // Log admin action
    await SystemLog.logAdmin(
      'user_deleted',
      req.user._id,
      req.user.email,
      req.ip,
      {
        deletedUser: deletedUserInfo
      }
    );

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Error in delete user:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
