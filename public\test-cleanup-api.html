<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Cleanup API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .test-button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-button.cleanup {
            background: #e74c3c;
        }
        .test-button.cleanup:hover {
            background: #c0392b;
        }
        .test-button.dry-run {
            background: #f39c12;
        }
        .test-button.dry-run:hover {
            background: #e67e22;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .cleanup-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .cleanup-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .cleanup-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .cleanup-card .days-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #e74c3c;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 Cleanup API Test Page</h1>
        <p>This page tests the updated cleanup API with the new days-based logic.</p>
        
        <div class="cleanup-options">
            <div class="cleanup-card">
                <h4>4 Hours</h4>
                <div class="days-value">days = 0.167</div>
                <p>Delete logs older than 4 hours</p>
            </div>
            <div class="cleanup-card">
                <h4>3 Days</h4>
                <div class="days-value">days = 3</div>
                <p>Delete logs older than 3 days</p>
            </div>
            <div class="cleanup-card">
                <h4>30 Days</h4>
                <div class="days-value">days = 30</div>
                <p>Delete logs older than 30 days</p>
            </div>
            <div class="cleanup-card">
                <h4>1 Year</h4>
                <div class="days-value">days = 365</div>
                <p>Delete logs older than 1 year</p>
            </div>
        </div>

        <div class="test-controls">
            <button class="test-button" onclick="testLogsStats()">📊 Get Current Stats</button>
            <button class="test-button dry-run" onclick="testCleanupDryRun(0.167)">🔍 Dry Run: 4 Hours</button>
            <button class="test-button dry-run" onclick="testCleanupDryRun(3)">🔍 Dry Run: 3 Days</button>
            <button class="test-button dry-run" onclick="testCleanupDryRun(30)">🔍 Dry Run: 30 Days</button>
            <button class="test-button dry-run" onclick="testCleanupDryRun(365)">🔍 Dry Run: 1 Year</button>
            <button class="test-button cleanup" onclick="testCleanupReal(0.167)">🗑️ Cleanup: 4 Hours</button>
            <button class="test-button cleanup" onclick="testCleanupReal(3)">🗑️ Cleanup: 3 Days</button>
            <button class="test-button cleanup" onclick="testCleanupReal(30)">🗑️ Cleanup: 30 Days</button>
            <button class="test-button cleanup" onclick="testCleanupReal(365)">🗑️ Cleanup: 1 Year</button>
            <button class="test-button" onclick="clearResults()">🧽 Clear Results</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4M2NlOWVlMmJmNGQ4MGJiYmQwYzg5ZCIsImlhdCI6MTc0ODgyNjk2MCwiZXhwIjoxNzUxNDE4OTYwfQ.ljq9SiYZH_MLkPux_NY-Q6OrsXRGeakyk8w76sHQ0co';

        function addResult(title, content, type = 'result') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLogsStats() {
            try {
                addResult('🔄 Getting current log statistics...', 'Making request...');
                
                const response = await fetch(`${API_BASE}/admin/logs/stats`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    const summary = data.data.summary;
                    const statsText = `Total Logs: ${summary.totalLogs}
Errors: ${summary.errors}
Warnings: ${summary.warnings}
Info: ${summary.info}
Debug: ${summary.debug}
Critical: ${summary.critical}`;
                    
                    addResult('✅ Current Log Statistics', statsText, 'success');
                } else {
                    addResult('❌ Failed to get stats', `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('❌ Network Error', `Error: ${error.message}`, 'error');
            }
        }

        async function testCleanupDryRun(days) {
            try {
                const displayName = days < 1 ? `${Math.round(days * 24)} hours` : 
                                   days >= 365 ? `${Math.round(days / 365)} year` : 
                                   `${days} days`;
                
                addResult(`🔍 Dry Run: Cleanup ${displayName}`, `Testing with days = ${days}...`);
                
                const response = await fetch(`${API_BASE}/admin/logs/cleanup`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        days: days,
                        dryRun: true
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    const resultText = `${data.message}
                    
Request: { "days": ${days}, "dryRun": true }
Response: ${JSON.stringify(data.data, null, 2)}`;
                    
                    addResult(`✅ Dry Run Success: ${displayName}`, resultText, 'warning');
                } else {
                    addResult(`❌ Dry Run Failed: ${displayName}`, `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Dry Run Error: ${displayName}`, `Error: ${error.message}`, 'error');
            }
        }

        async function testCleanupReal(days) {
            const displayName = days < 1 ? `${Math.round(days * 24)} hours` : 
                               days >= 365 ? `${Math.round(days / 365)} year` : 
                               `${days} days`;
            
            if (!confirm(`⚠️ Are you sure you want to PERMANENTLY DELETE logs older than ${displayName}?\n\nThis action cannot be undone!`)) {
                return;
            }
            
            try {
                addResult(`🗑️ Real Cleanup: ${displayName}`, `Deleting logs with days = ${days}...`);
                
                const response = await fetch(`${API_BASE}/admin/logs/cleanup`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${AUTH_TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        days: days,
                        dryRun: false
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    const resultText = `${data.message}
                    
Request: { "days": ${days}, "dryRun": false }
Response: ${JSON.stringify(data.data, null, 2)}`;
                    
                    addResult(`✅ Cleanup Complete: ${displayName}`, resultText, 'success');
                    
                    // Auto-refresh stats after cleanup
                    setTimeout(() => {
                        testLogsStats();
                    }, 1000);
                } else {
                    addResult(`❌ Cleanup Failed: ${displayName}`, `Status: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Cleanup Error: ${displayName}`, `Error: ${error.message}`, 'error');
            }
        }

        // Auto-load stats on page load
        window.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testLogsStats();
            }, 500);
        });
    </script>
</body>
</html>
