import React, { useState, useEffect, useCallback } from 'react';
import { AdminLogsAPI, LogUtils } from './admin-logs-api.js';

// Initialize the API instance
const logsAPI = new AdminLogsAPI('http://localhost:5000/api', () => localStorage.getItem('authToken'));

const AdminLogsComponent = () => {
    // State management
    const [stats, setStats] = useState({
        totalLogs: 0,
        errors: 0,
        warnings: 0,
        info: 0,
        debug: 0,
        critical: 0
    });
    const [logs, setLogs] = useState([]);
    const [pagination, setPagination] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    
    // Filter state
    const [filters, setFilters] = useState({
        page: 1,
        limit: 20,
        level: '',
        category: '',
        search: ''
    });

    // Fetch log statistics
    const fetchStats = useCallback(async () => {
        try {
            const statsData = await logsAPI.getLogStats();
            setStats(statsData.summary);
        } catch (err) {
            console.error('Failed to fetch stats:', err);
            setError('Failed to load statistics');
        }
    }, []);

    // Fetch logs with current filters
    const fetchLogs = useCallback(async () => {
        try {
            setLoading(true);
            const logsData = await logsAPI.getLogs(filters);
            setLogs(logsData.logs);
            setPagination(logsData.pagination);
            setError(null);
        } catch (err) {
            console.error('Failed to fetch logs:', err);
            setError('Failed to load logs');
        } finally {
            setLoading(false);
        }
    }, [filters]);

    // Handle filter changes
    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value,
            page: key !== 'page' ? 1 : value // Reset to page 1 when changing filters
        }));
    };

    // Handle page change
    const handlePageChange = (newPage) => {
        setFilters(prev => ({ ...prev, page: newPage }));
    };

    // Export logs
    const handleExport = async (format) => {
        try {
            const blob = await logsAPI.exportLogs(format);
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-logs.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (err) {
            console.error('Failed to export logs:', err);
            setError('Failed to export logs');
        }
    };

    // Initial data load
    useEffect(() => {
        fetchStats();
        fetchLogs();
    }, [fetchStats, fetchLogs]);

    // Auto-refresh every 30 seconds
    useEffect(() => {
        const interval = setInterval(() => {
            fetchStats();
            fetchLogs();
        }, 30000);

        return () => clearInterval(interval);
    }, [fetchStats, fetchLogs]);

    return (
        <div className="admin-logs-container">
            {/* Header */}
            <div className="logs-header">
                <h1>System Logs</h1>
                <p>Monitor and analyze system activity and events</p>
            </div>

            {/* Statistics Cards */}
            <div className="stats-grid">
                <StatCard 
                    title="Total Logs" 
                    value={stats.totalLogs} 
                    className="total"
                />
                <StatCard 
                    title="Errors" 
                    value={stats.errors} 
                    className="errors"
                />
                <StatCard 
                    title="Warnings" 
                    value={stats.warnings} 
                    className="warnings"
                />
                <StatCard 
                    title="Info" 
                    value={stats.info} 
                    className="info"
                />
            </div>

            {/* Error Display */}
            {error && (
                <div className="error-message">
                    {error}
                    <button onClick={() => setError(null)}>×</button>
                </div>
            )}

            {/* Logs Section */}
            <div className="logs-section">
                <div className="section-header">
                    <span>Recent System Logs</span>
                    <div className="export-buttons">
                        <button onClick={() => handleExport('json')}>Export JSON</button>
                        <button onClick={() => handleExport('csv')}>Export CSV</button>
                    </div>
                </div>

                {/* Filters */}
                <LogFilters 
                    filters={filters}
                    onFilterChange={handleFilterChange}
                />

                {/* Logs Table */}
                {loading ? (
                    <div className="loading">Loading logs...</div>
                ) : (
                    <LogsTable logs={logs} />
                )}

                {/* Pagination */}
                {pagination && (
                    <LogsPagination 
                        pagination={pagination}
                        onPageChange={handlePageChange}
                    />
                )}
            </div>
        </div>
    );
};

// Statistics Card Component
const StatCard = ({ title, value, className }) => (
    <div className={`stat-card ${className}`}>
        <h3>{title}</h3>
        <div className="stat-number">{value.toLocaleString()}</div>
    </div>
);

// Filters Component
const LogFilters = ({ filters, onFilterChange }) => (
    <div className="filters">
        <div className="filter-group">
            <label>Level</label>
            <select 
                value={filters.level} 
                onChange={(e) => onFilterChange('level', e.target.value)}
            >
                <option value="">All Levels</option>
                <option value="info">Info</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="debug">Debug</option>
                <option value="critical">Critical</option>
            </select>
        </div>
        
        <div className="filter-group">
            <label>Category</label>
            <select 
                value={filters.category} 
                onChange={(e) => onFilterChange('category', e.target.value)}
            >
                <option value="">All Categories</option>
                <option value="auth">Authentication</option>
                <option value="admin">Admin</option>
                <option value="api">API</option>
                <option value="database">Database</option>
                <option value="security">Security</option>
                <option value="system">System</option>
                <option value="upload">Upload</option>
            </select>
        </div>
        
        <div className="filter-group">
            <label>Search</label>
            <input 
                type="text" 
                value={filters.search}
                onChange={(e) => onFilterChange('search', e.target.value)}
                placeholder="Search logs..."
            />
        </div>
    </div>
);

// Logs Table Component
const LogsTable = ({ logs }) => {
    if (logs.length === 0) {
        return <div className="no-logs">No logs found</div>;
    }

    return (
        <table className="logs-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Level</th>
                    <th>Category</th>
                    <th>Action</th>
                    <th>Message</th>
                    <th>User</th>
                </tr>
            </thead>
            <tbody>
                {logs.map((log) => (
                    <LogRow key={log._id} log={log} />
                ))}
            </tbody>
        </table>
    );
};

// Individual Log Row Component
const LogRow = ({ log }) => {
    const levelInfo = LogUtils.formatLevel(log.level);
    
    return (
        <tr>
            <td>{LogUtils.formatDate(log.createdAt)}</td>
            <td>
                <span 
                    className="log-level"
                    style={{ 
                        color: levelInfo.color, 
                        backgroundColor: levelInfo.background 
                    }}
                >
                    {levelInfo.level}
                </span>
            </td>
            <td>{log.category}</td>
            <td>{log.action}</td>
            <td title={log.message}>
                {log.message.length > 50 
                    ? `${log.message.substring(0, 50)}...` 
                    : log.message
                }
            </td>
            <td>{LogUtils.formatUser(log.userId)}</td>
        </tr>
    );
};

// Pagination Component
const LogsPagination = ({ pagination, onPageChange }) => (
    <div className="pagination">
        <button 
            onClick={() => onPageChange(pagination.current - 1)}
            disabled={pagination.current <= 1}
        >
            Previous
        </button>
        
        <span className="page-info">
            Page {pagination.current} of {pagination.pages}
        </span>
        
        <button 
            onClick={() => onPageChange(pagination.current + 1)}
            disabled={pagination.current >= pagination.pages}
        >
            Next
        </button>
    </div>
);

export default AdminLogsComponent;
