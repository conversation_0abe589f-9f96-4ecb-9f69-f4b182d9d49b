const express = require('express');
const router = express.Router();
const Gallery = require('../models/Gallery');

// @route   GET /api/gallery
// @desc    Get active gallery items for public display
// @access  Public
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 12,
      category = '',
      featured = '',
      search = ''
    } = req.query;

    // Build query for active items only
    const query = { isActive: true };
    
    if (category) {
      query.category = category;
    }
    
    if (featured === 'true') {
      query.isFeatured = true;
    }
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get gallery items
    const galleryItems = await Gallery.find(query)
      .select('title description imageUrl slug category tags altText isFeatured displayOrder createdAt')
      .sort({ displayOrder: 1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await Gallery.countDocuments(query);

    res.json({
      success: true,
      data: {
        galleryItems,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });

  } catch (error) {
    console.error('Error in get public gallery:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/gallery/featured
// @desc    Get featured gallery items
// @access  Public
router.get('/featured', async (req, res) => {
  try {
    const { limit = 6 } = req.query;

    const featuredItems = await Gallery.find({
      isActive: true,
      isFeatured: true
    })
      .select('title description imageUrl slug category altText displayOrder createdAt')
      .sort({ displayOrder: 1, createdAt: -1 })
      .limit(parseInt(limit));

    res.json({
      success: true,
      data: {
        featuredItems,
        count: featuredItems.length
      }
    });

  } catch (error) {
    console.error('Error in get featured gallery:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/gallery/categories
// @desc    Get available gallery categories
// @access  Public
router.get('/categories', async (req, res) => {
  try {
    const categories = await Gallery.distinct('category', { isActive: true });
    
    // Get count for each category
    const categoriesWithCount = await Promise.all(
      categories.map(async (category) => {
        const count = await Gallery.countDocuments({
          category,
          isActive: true
        });
        return { category, count };
      })
    );

    res.json({
      success: true,
      data: {
        categories: categoriesWithCount
      }
    });

  } catch (error) {
    console.error('Error in get gallery categories:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/gallery/:slug
// @desc    Get gallery item by slug
// @access  Public
router.get('/:slug', async (req, res) => {
  try {
    const galleryItem = await Gallery.findOne({
      slug: req.params.slug,
      isActive: true
    }).select('title description imageUrl slug category tags altText createdAt');
    
    if (!galleryItem) {
      return res.status(404).json({
        success: false,
        message: 'Gallery item not found'
      });
    }

    res.json({
      success: true,
      data: { galleryItem }
    });

  } catch (error) {
    console.error('Error in get gallery item by slug:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
