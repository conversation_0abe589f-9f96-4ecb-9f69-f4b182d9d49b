/**
 * Test script for Gemini API integration
 */

require('dotenv').config();
const GeminiService = require('./services/geminiService');

async function testGeminiService() {
  console.log('🧪 Testing Gemini API Service...\n');
  
  try {
    const geminiService = new GeminiService();
    
    // Test 1: Connection test
    console.log('1️⃣ Testing API connection...');
    const connectionTest = await geminiService.testConnection();
    
    if (connectionTest.success) {
      console.log('✅ Connection successful!');
      console.log(`📝 Response: ${connectionTest.response}`);
    } else {
      console.log('❌ Connection failed!');
      console.log(`📝 Error: ${connectionTest.error}`);
      return;
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Test 2: Text-only analysis (no image)
    console.log('2️⃣ Testing text analysis...');
    
    const textAnalysisPayload = {
      contents: [
        {
          parts: [
            {
              text: geminiService.getSystemPrompt() + "\n\nAnalyze this text description: 'A hamburger with fries and a soft drink'"
            }
          ]
        }
      ]
    };
    
    const axios = require('axios');
    const textResponse = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${process.env.GEMINI_API_KEY}`,
      textAnalysisPayload,
      {
        headers: { 'Content-Type': 'application/json' },
        timeout: 30000
      }
    );
    
    const textResult = textResponse.data?.candidates?.[0]?.content?.parts?.[0]?.text;
    console.log('✅ Text analysis successful!');
    console.log('📝 Sample response (first 200 chars):');
    console.log(textResult.substring(0, 200) + '...');
    
    // Try to parse as JSON
    try {
      const cleanedText = textResult.replace(/```json\n?|\n?```/g, '').trim();
      const parsed = JSON.parse(cleanedText);
      console.log('✅ Response is valid JSON!');
      console.log(`📊 Food items found: ${parsed.food_items?.length || 0}`);
      if (parsed.food_items && parsed.food_items.length > 0) {
        console.log(`🍔 First item: ${parsed.food_items[0].name}`);
      }
    } catch (parseError) {
      console.log('⚠️  Response is not valid JSON, but that\'s okay for text-only test');
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    console.log('🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Gemini API connection: Working');
    console.log('✅ Service initialization: Working');
    console.log('✅ Text analysis: Working');
    console.log('\n🚀 Ready for image analysis!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('📝 API Error Details:', error.response.data);
    }
  }
}

// Run the test
testGeminiService();
