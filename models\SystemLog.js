const mongoose = require('mongoose');

const SystemLogSchema = new mongoose.Schema({
  level: {
    type: String,
    enum: ['info', 'warning', 'error', 'debug', 'critical'],
    required: true,
    index: true
  },
  category: {
    type: String,
    enum: ['auth', 'user', 'admin', 'api', 'database', 'security', 'system', 'upload'],
    required: true,
    index: true
  },
  action: {
    type: String,
    required: true
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: mongoose.Schema.Types.Mixed
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  userEmail: String,
  ipAddress: String,
  userAgent: String,
  endpoint: String,
  method: String,
  statusCode: Number,
  responseTime: Number, // in milliseconds
  errorStack: String,
  metadata: {
    type: mongoose.Schema.Types.Mixed
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  }
});

// Static method to log events
SystemLogSchema.statics.logEvent = async function(logData) {
  try {
    const log = new this(logData);
    await log.save();
    return log;
  } catch (error) {
    console.error('Failed to save system log:', error);
  }
};

// Static method to log authentication events
SystemLogSchema.statics.logAuth = async function(action, userId, userEmail, ipAddress, details = {}) {
  return await this.logEvent({
    level: 'info',
    category: 'auth',
    action,
    message: `Authentication event: ${action}`,
    userId,
    userEmail,
    ipAddress,
    details
  });
};

// Static method to log admin actions
SystemLogSchema.statics.logAdmin = async function(action, userId, userEmail, ipAddress, details = {}) {
  return await this.logEvent({
    level: 'info',
    category: 'admin',
    action,
    message: `Admin action: ${action}`,
    userId,
    userEmail,
    ipAddress,
    details
  });
};

// Static method to log errors
SystemLogSchema.statics.logError = async function(error, userId = null, ipAddress = null, endpoint = null) {
  return await this.logEvent({
    level: 'error',
    category: 'system',
    action: 'error',
    message: error.message || 'Unknown error',
    userId,
    ipAddress,
    endpoint,
    errorStack: error.stack,
    details: {
      name: error.name,
      code: error.code
    }
  });
};

// Static method to get recent logs
SystemLogSchema.statics.getRecentLogs = async function(limit = 100, level = null, category = null) {
  const query = {};
  
  if (level) query.level = level;
  if (category) query.category = category;
  
  return await this.find(query)
    .populate('userId', 'firstName lastName email')
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Static method to get log statistics
SystemLogSchema.statics.getLogStats = async function(days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: {
          level: '$level',
          category: '$category',
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt'
            }
          }
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: {
          level: '$_id.level',
          category: '$_id.category'
        },
        totalCount: { $sum: '$count' },
        dailyBreakdown: {
          $push: {
            date: '$_id.date',
            count: '$count'
          }
        }
      }
    }
  ]);
  
  return stats;
};

// Create TTL index to automatically delete old logs (keep for 90 days)
SystemLogSchema.index({ createdAt: 1 }, { expireAfterSeconds: 90 * 24 * 60 * 60 });

// Create compound indexes for better query performance
SystemLogSchema.index({ level: 1, createdAt: -1 });
SystemLogSchema.index({ category: 1, createdAt: -1 });
SystemLogSchema.index({ userId: 1, createdAt: -1 });

module.exports = mongoose.model('SystemLog', SystemLogSchema);
