const mongoose = require('mongoose');

const SystemConfigSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    required: true
  },
  category: {
    type: String,
    enum: ['general', 'appearance', 'security', 'email', 'api', 'features'],
    default: 'general'
  },
  description: {
    type: String,
    trim: true
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  isEditable: {
    type: Boolean,
    default: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
SystemConfigSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Static method to get config value
SystemConfigSchema.statics.getConfig = async function(key, defaultValue = null) {
  try {
    const config = await this.findOne({ key });
    return config ? config.value : defaultValue;
  } catch (error) {
    return defaultValue;
  }
};

// Static method to set config value
SystemConfigSchema.statics.setConfig = async function(key, value, type, userId = null) {
  try {
    const updateData = {
      value,
      type,
      updatedBy: userId,
      updatedAt: new Date()
    };

    const config = await this.findOneAndUpdate(
      { key },
      updateData,
      { upsert: true, new: true }
    );
    
    return config;
  } catch (error) {
    throw error;
  }
};

// Static method to get all public configs
SystemConfigSchema.statics.getPublicConfigs = async function() {
  try {
    const configs = await this.find({ isPublic: true }).select('key value type');
    const result = {};
    configs.forEach(config => {
      result[config.key] = config.value;
    });
    return result;
  } catch (error) {
    return {};
  }
};

// Create indexes
SystemConfigSchema.index({ key: 1 });
SystemConfigSchema.index({ category: 1 });
SystemConfigSchema.index({ isPublic: 1 });

module.exports = mongoose.model('SystemConfig', SystemConfigSchema);
