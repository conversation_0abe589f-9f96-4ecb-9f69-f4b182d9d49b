# Settings API Documentation

## Overview
The Settings API allows users to manage their account preferences, notification settings, dietary restrictions, nutrition goals, and account security.

## Base URL
```
http://localhost:5000/api/users
```

## Authentication
All endpoints require authentication via Bear<PERSON> token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

---

## Endpoints

### 1. Get User Profile
**GET** `/profile`

Returns the current user's profile including all preferences.

**Response:**
```json
{
  "success": true,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "isAdmin": false,
    "preferences": {
      "dietaryRestrictions": ["vegetarian"],
      "nutritionGoals": {
        "calories": 2000,
        "protein": 150,
        "carbs": 200,
        "fat": 65
      },
      "units": "metric",
      "notifications": {
        "mealReminders": true,
        "weeklyReports": true,
        "achievementAlerts": true,
        "emailNotifications": true
      }
    }
  }
}
```

---

### 2. Update Settings (Comprehensive)
**PUT** `/settings`

Updates user settings including preferences, notifications, and optionally password.

**Request Body:**
```json
{
  "notifications": {
    "mealReminders": false,
    "weeklyReports": true,
    "achievementAlerts": false,
    "emailNotifications": true
  },
  "dietaryRestrictions": ["vegetarian", "gluten-free"],
  "nutritionGoals": {
    "calories": 2200,
    "protein": 140,
    "carbs": 275,
    "fat": 75
  },
  "units": "imperial",
  "currentPassword": "current_password",
  "newPassword": "new_password",
  "confirmPassword": "new_password"
}
```

**Notes:**
- All fields are optional
- Password fields are only required when changing password
- If changing password, all three password fields must be provided

**Response:**
```json
{
  "success": true,
  "message": "Settings updated successfully",
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "user",
    "isAdmin": false,
    "preferences": {
      // Updated preferences
    }
  }
}
```

---

### 3. Update Preferences (Legacy)
**PUT** `/preferences`

Legacy endpoint for updating only dietary restrictions, nutrition goals, and units.

**Request Body:**
```json
{
  "dietaryRestrictions": ["vegetarian"],
  "nutritionGoals": {
    "calories": 2000,
    "protein": 150,
    "carbs": 200,
    "fat": 65
  },
  "units": "metric"
}
```

---

### 4. Delete Account
**DELETE** `/account`

Permanently deletes the user's account.

**Request Body:**
```json
{
  "password": "user_password"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account deleted successfully"
}
```

---

## Data Types

### Dietary Restrictions
Array of strings. Supported values:
- `"vegetarian"`
- `"vegan"`
- `"gluten-free"`
- `"dairy-free"`
- `"nut-free"`
- `"pescatarian"`
- `"paleo"`
- `"keto"`
- `"halal"`
- `"kosher"`

### Units
String enum:
- `"metric"` - grams, centimeters
- `"imperial"` - ounces, inches

### Nutrition Goals
Object with numeric values:
- `calories`: Daily calorie goal
- `protein`: Daily protein goal (grams)
- `carbs`: Daily carbohydrates goal (grams)
- `fat`: Daily fat goal (grams)

### Notifications
Object with boolean values:
- `mealReminders`: Enable meal reminder notifications
- `weeklyReports`: Enable weekly nutrition reports
- `achievementAlerts`: Enable achievement notifications
- `emailNotifications`: Enable email notifications

---

## Error Responses

### Validation Errors
```json
{
  "success": false,
  "errors": [
    {
      "msg": "Dietary restrictions must be an array",
      "param": "dietaryRestrictions",
      "location": "body"
    }
  ]
}
```

### Authentication Errors
```json
{
  "success": false,
  "message": "Access denied. No token provided."
}
```

### Password Errors
```json
{
  "success": false,
  "message": "Current password is incorrect"
}
```

### Server Errors
```json
{
  "success": false,
  "message": "Server error"
}
```

---

## Frontend Integration Example

```javascript
// Update settings
const updateSettings = async (settingsData) => {
  try {
    const response = await fetch('/api/users/settings', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      },
      body: JSON.stringify(settingsData)
    });

    if (!response.ok) {
      throw new Error('Failed to update settings');
    }

    const data = await response.json();
    console.log('Settings updated:', data);
    return data;
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};
```
