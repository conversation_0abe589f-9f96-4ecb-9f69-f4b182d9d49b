const express = require('express');
const router = express.Router();
const { protect, adminPanel, admin, requireRole } = require('../middleware/auth');
const { adminLimiter } = require('../middleware/security');
const User = require('../models/User');
const FoodAnalysis = require('../models/FoodAnalysis');
const FoodDatabase = require('../models/FoodDatabase');
const Announcement = require('../models/Announcement');
const Gallery = require('../models/Gallery');
const VisitorTracking = require('../models/VisitorTracking');
const SystemLog = require('../models/SystemLog');
const SystemConfig = require('../models/SystemConfig');

// Apply rate limiting and authentication to all admin routes
router.use(adminLimiter);
router.use(protect);
router.use(adminPanel);

// @route   GET /api/admin/dashboard
// @desc    Get admin dashboard statistics
// @access  Private (Admin Panel Access)
router.get('/dashboard', async (req, res) => {
  try {
    // Get current date for statistics
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const thisWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    // Get user statistics
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ status: 'active' });
    const newUsersToday = await User.countDocuments({ createdAt: { $gte: today } });
    const newUsersThisWeek = await User.countDocuments({ createdAt: { $gte: thisWeek } });

    // Get food analysis statistics
    const totalAnalyses = await FoodAnalysis.countDocuments();
    const analysesToday = await FoodAnalysis.countDocuments({ createdAt: { $gte: today } });
    const analysesThisWeek = await FoodAnalysis.countDocuments({ createdAt: { $gte: thisWeek } });

    // Get food database statistics
    const totalFoodItems = await FoodDatabase.countDocuments();

    // Get announcement statistics
    const totalAnnouncements = await Announcement.countDocuments();
    const activeAnnouncements = await Announcement.countDocuments({
      publishDate: { $lte: now },
      expiryDate: { $gte: now }
    });

    // Get gallery statistics
    const totalGalleryItems = await Gallery.countDocuments();
    const activeGalleryItems = await Gallery.countDocuments({ isActive: true });

    // Get visitor statistics
    const onlineUsers = await VisitorTracking.getOnlineCount();
    const todayVisitorStats = await VisitorTracking.getDailyStats(today);

    // Get recent system logs
    const recentLogs = await SystemLog.getRecentLogs(5);

    // Generate recent activity from various sources
    const recentActivity = [];

    // Add recent user registrations
    const recentUsers = await User.find({ createdAt: { $gte: thisWeek } })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('firstName lastName email createdAt');

    recentUsers.forEach(user => {
      recentActivity.push({
        id: `user_${user._id}`,
        type: 'user_registration',
        description: `New user registered: ${user.firstName} ${user.lastName}`,
        timestamp: user.createdAt,
        user: {
          id: user._id,
          name: `${user.firstName} ${user.lastName}`
        }
      });
    });

    // Add recent gallery uploads
    const recentGalleryItems = await Gallery.find({ createdAt: { $gte: thisWeek } })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('title createdAt');

    recentGalleryItems.forEach(item => {
      recentActivity.push({
        id: `gallery_${item._id}`,
        type: 'gallery_upload',
        description: `New gallery item added: ${item.title}`,
        timestamp: item.createdAt
      });
    });

    // Add recent announcements
    const recentAnnouncements = await Announcement.find({ createdAt: { $gte: thisWeek } })
      .sort({ createdAt: -1 })
      .limit(3)
      .select('title createdAt');

    recentAnnouncements.forEach(announcement => {
      recentActivity.push({
        id: `announcement_${announcement._id}`,
        type: 'announcement',
        description: `New announcement published: ${announcement.title}`,
        timestamp: announcement.createdAt
      });
    });

    // Sort all activity by timestamp (most recent first) and limit to 5
    recentActivity.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    const limitedRecentActivity = recentActivity.slice(0, 5);

    // Get system health
    const systemHealth = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV
    };

    res.json({
      success: true,
      data: {
        users: {
          total: totalUsers,
          active: activeUsers,
          newToday: newUsersToday,
          newThisWeek: newUsersThisWeek
        },
        foodAnalyses: {
          total: totalAnalyses,
          today: analysesToday,
          thisWeek: analysesThisWeek
        },
        foodDatabase: {
          total: totalFoodItems
        },
        announcements: {
          total: totalAnnouncements,
          active: activeAnnouncements
        },
        gallery: {
          total: totalGalleryItems,
          active: activeGalleryItems
        },
        visitors: {
          online: onlineUsers,
          todayStats: todayVisitorStats
        },
        recentActivity: limitedRecentActivity,
        recentLogs,
        systemHealth
      }
    });

    // Log admin dashboard access
    await SystemLog.logAdmin(
      'dashboard_access',
      req.user._id,
      req.user.email,
      req.ip,
      { timestamp: new Date() }
    );

  } catch (error) {
    console.error('Error in admin dashboard:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/analytics
// @desc    Get detailed analytics data
// @access  Private (Admin Panel Access)
router.get('/analytics', async (req, res) => {
  try {
    const { period = '7', type = 'overview' } = req.query;
    const days = parseInt(period);
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    let analyticsData = {};

    switch (type) {
      case 'users':
        // User registration trends
        const userTrends = await User.aggregate([
          {
            $match: { createdAt: { $gte: startDate } }
          },
          {
            $group: {
              _id: {
                $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
              },
              count: { $sum: 1 }
            }
          },
          { $sort: { _id: 1 } }
        ]);

        analyticsData.userTrends = userTrends;
        break;

      case 'activity':
        // Food analysis activity
        const activityTrends = await FoodAnalysis.aggregate([
          {
            $match: { createdAt: { $gte: startDate } }
          },
          {
            $group: {
              _id: {
                date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                category: '$mealCategory'
              },
              count: { $sum: 1 }
            }
          },
          { $sort: { '_id.date': 1 } }
        ]);

        analyticsData.activityTrends = activityTrends;
        break;

      case 'visitors':
        // Visitor analytics
        const visitorStats = await VisitorTracking.aggregate([
          {
            $match: { createdAt: { $gte: startDate } }
          },
          {
            $group: {
              _id: {
                $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
              },
              visitors: { $sum: 1 },
              pageViews: { $sum: '$pageViews' },
              avgTimeSpent: { $avg: '$timeSpent' }
            }
          },
          { $sort: { _id: 1 } }
        ]);

        analyticsData.visitorStats = visitorStats;
        break;

      default:
        // Overview analytics
        const logStats = await SystemLog.getLogStats(days);
        analyticsData.logStats = logStats;
    }

    res.json({
      success: true,
      data: analyticsData
    });

  } catch (error) {
    console.error('Error in admin analytics:', error);
    await SystemLog.logError(error, req.user._id, req.ip, req.originalUrl);
    
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/admin/system-info
// @desc    Get system information
// @access  Private (Admin Only)
router.get('/system-info', requireRole(['admin']), async (req, res) => {
  try {
    const systemInfo = {
      server: {
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage()
      },
      database: {
        // Add database connection info if needed
        connected: true // This would be dynamic based on actual connection status
      },
      application: {
        version: require('../package.json').version || '1.0.0',
        name: require('../package.json').name || 'nutrisnap-backend'
      }
    };

    res.json({
      success: true,
      data: systemInfo
    });

  } catch (error) {
    console.error('Error in system info:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

module.exports = router;
