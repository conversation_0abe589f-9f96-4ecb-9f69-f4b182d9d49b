const multer = require('multer');

// Configure memory storage for Cloudinary
const storage = multer.memoryStorage();

// File filter
const fileFilter = (_req, file, cb) => {
  // Accept images only (including AVIF)
  if (!file.originalname.match(/\.(jpg|jpeg|png|gif|avif|webp)$/i)) {
    return cb(new Error('Only image files are allowed! Supported formats: JPG, JPEG, PNG, GIF, AVIF, WebP'), false);
  }
  cb(null, true);
};

// Create upload middleware
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB max file size
  },
  fileFilter: fileFilter
});

module.exports = upload;
