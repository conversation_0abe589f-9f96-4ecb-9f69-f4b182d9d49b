// Frontend Settings Integration Example
// This shows how to integrate the settings API with your frontend

class SettingsManager {
  constructor(apiBaseUrl = 'http://localhost:5000/api') {
    this.apiBaseUrl = apiBaseUrl;
    this.token = localStorage.getItem('authToken');
  }

  // Get authorization headers
  getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.token}`
    };
  }

  // Get current user settings
  async getCurrentSettings() {
    try {
      const response = await fetch(`${this.apiBaseUrl}/users/profile`, {
        method: 'GET',
        headers: this.getHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const data = await response.json();
      return data.user.preferences;
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error;
    }
  }

  // Update user settings
  async updateSettings(settingsData) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/users/settings`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(settingsData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update settings');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  }

  // Delete user account
  async deleteAccount(password) {
    try {
      const response = await fetch(`${this.apiBaseUrl}/users/account`, {
        method: 'DELETE',
        headers: this.getHeaders(),
        body: JSON.stringify({ password })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete account');
      }

      const data = await response.json();
      // Clear local storage on successful deletion
      localStorage.removeItem('authToken');
      return data;
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }
}

// Example usage in your settings page
class SettingsPage {
  constructor() {
    this.settingsManager = new SettingsManager();
    this.currentSettings = null;
    this.init();
  }

  async init() {
    try {
      // Load current settings
      this.currentSettings = await this.settingsManager.getCurrentSettings();
      this.populateForm();
    } catch (error) {
      this.showError('Failed to load settings');
    }
  }

  // Populate form with current settings
  populateForm() {
    if (!this.currentSettings) return;

    // Notification settings
    document.getElementById('mealReminders').checked = this.currentSettings.notifications.mealReminders;
    document.getElementById('weeklyReports').checked = this.currentSettings.notifications.weeklyReports;
    document.getElementById('achievementAlerts').checked = this.currentSettings.notifications.achievementAlerts;

    // Dietary restrictions
    this.currentSettings.dietaryRestrictions.forEach(restriction => {
      const checkbox = document.getElementById(restriction);
      if (checkbox) checkbox.checked = true;
    });

    // Nutrition goals
    document.getElementById('calorieGoal').value = this.currentSettings.nutritionGoals.calories;
    document.getElementById('proteinGoal').value = this.currentSettings.nutritionGoals.protein;
    document.getElementById('carbsGoal').value = this.currentSettings.nutritionGoals.carbs;
    document.getElementById('fatGoal').value = this.currentSettings.nutritionGoals.fat;

    // Units
    document.getElementById('units').value = this.currentSettings.units;
  }

  // Handle save changes
  async handleSaveChanges() {
    try {
      const formData = this.getFormData();
      const result = await this.settingsManager.updateSettings(formData);
      
      this.showSuccess('Settings updated successfully!');
      this.currentSettings = result.user.preferences;
    } catch (error) {
      this.showError(error.message);
    }
  }

  // Get form data
  getFormData() {
    return {
      notifications: {
        mealReminders: document.getElementById('mealReminders').checked,
        weeklyReports: document.getElementById('weeklyReports').checked,
        achievementAlerts: document.getElementById('achievementAlerts').checked,
        emailNotifications: document.getElementById('emailNotifications').checked
      },
      dietaryRestrictions: this.getSelectedDietaryRestrictions(),
      nutritionGoals: {
        calories: parseInt(document.getElementById('calorieGoal').value),
        protein: parseInt(document.getElementById('proteinGoal').value),
        carbs: parseInt(document.getElementById('carbsGoal').value),
        fat: parseInt(document.getElementById('fatGoal').value)
      },
      units: document.getElementById('units').value,
      // Password change (only if provided)
      ...(this.getPasswordChangeData())
    };
  }

  // Get selected dietary restrictions
  getSelectedDietaryRestrictions() {
    const restrictions = [];
    const checkboxes = document.querySelectorAll('input[name="dietaryRestrictions"]:checked');
    checkboxes.forEach(checkbox => {
      restrictions.push(checkbox.value);
    });
    return restrictions;
  }

  // Get password change data if provided
  getPasswordChangeData() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (currentPassword && newPassword && confirmPassword) {
      return {
        currentPassword,
        newPassword,
        confirmPassword
      };
    }
    return {};
  }

  // Handle account deletion
  async handleDeleteAccount() {
    const password = prompt('Please enter your password to confirm account deletion:');
    if (!password) return;

    if (confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      try {
        await this.settingsManager.deleteAccount(password);
        alert('Account deleted successfully');
        window.location.href = '/login';
      } catch (error) {
        this.showError(error.message);
      }
    }
  }

  // Show success message
  showSuccess(message) {
    // Implement your success notification UI
    console.log('Success:', message);
  }

  // Show error message
  showError(message) {
    // Implement your error notification UI
    console.error('Error:', message);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  const settingsPage = new SettingsPage();
  
  // Bind event listeners
  document.getElementById('saveChanges').addEventListener('click', () => {
    settingsPage.handleSaveChanges();
  });

  document.getElementById('deleteAccount').addEventListener('click', () => {
    settingsPage.handleDeleteAccount();
  });
});

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { SettingsManager, SettingsPage };
}
