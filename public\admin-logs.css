/* Admin Logs Styles */
.admin-logs-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    min-height: 100vh;
}

/* Header */
.logs-header {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.logs-header h1 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 28px;
    font-weight: 600;
}

.logs-header p {
    color: #7f8c8d;
    margin: 0;
    font-size: 16px;
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.stat-card h3 {
    color: #7f8c8d;
    font-size: 14px;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.stat-number {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
    margin: 0;
}

/* Stat card color variants */
.stat-card.total .stat-number { color: #3498db; }
.stat-card.errors .stat-number { color: #e74c3c; }
.stat-card.warnings .stat-number { color: #f39c12; }
.stat-card.info .stat-number { color: #27ae60; }
.stat-card.debug .stat-number { color: #9b59b6; }
.stat-card.critical .stat-number { color: #c0392b; }

/* Error Message */
.error-message {
    background: #fadbd8;
    color: #e74c3c;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid #e74c3c;
}

.error-message button {
    background: none;
    border: none;
    color: #e74c3c;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Logs Section */
.logs-section {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    background: #34495e;
    color: white;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.export-buttons {
    display: flex;
    gap: 10px;
}

.export-buttons button {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.export-buttons button:hover {
    background: #2980b9;
}

/* Filters */
.filters {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 150px;
}

.filter-group label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-weight: 600;
}

.filter-group select,
.filter-group input {
    padding: 10px 12px;
    border: 1px solid #bdc3c7;
    border-radius: 4px;
    font-size: 14px;
    background: white;
    transition: border-color 0.2s;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Loading and No Logs */
.loading,
.no-logs {
    text-align: center;
    padding: 40px;
    color: #7f8c8d;
    font-size: 16px;
}

/* Logs Table */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.logs-table th,
.logs-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.logs-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    position: sticky;
    top: 0;
    z-index: 10;
}

.logs-table tbody tr:hover {
    background: #f8f9fa;
}

.logs-table td:first-child {
    white-space: nowrap;
}

.logs-table td:nth-child(5) {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Log Level Badge */
.log-level {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: inline-block;
    min-width: 60px;
    text-align: center;
}

/* Pagination */
.pagination {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #ecf0f1;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
}

.pagination button {
    padding: 10px 20px;
    border: 1px solid #bdc3c7;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.pagination button:hover:not(:disabled) {
    background: #ecf0f1;
    border-color: #95a5a6;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: #7f8c8d;
    font-size: 14px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-logs-container {
        padding: 10px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .filters {
        flex-direction: column;
        gap: 15px;
    }
    
    .filter-group {
        min-width: auto;
    }
    
    .section-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .export-buttons {
        justify-content: center;
    }
    
    .logs-table {
        font-size: 12px;
    }
    
    .logs-table th,
    .logs-table td {
        padding: 8px 10px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-number {
        font-size: 2em;
    }
    
    .logs-table th:nth-child(3),
    .logs-table td:nth-child(3),
    .logs-table th:nth-child(4),
    .logs-table td:nth-child(4) {
        display: none;
    }
}
