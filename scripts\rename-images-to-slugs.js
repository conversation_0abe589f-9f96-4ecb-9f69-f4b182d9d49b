// /**
//  * <PERSON><PERSON><PERSON> to rename existing images in uploads folder to use slug-based names
//  * and update the database accordingly
//  */

// require('dotenv').config();
// const mongoose = require('mongoose');
// const fs = require('fs').promises;
// const path = require('path');
// const Gallery = require('../models/Gallery');
// const FoodAnalysis = require('../models/FoodAnalysis');
// const { slugify } = require('../utils/slugify');

// // Connect to MongoDB
// async function connectDB() {
//   try {
//     await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/nutrisnap', {
//       useNewUrlParser: true,
//       useUnifiedTopology: true
//     });
//     console.log('MongoDB connected');
//   } catch (error) {
//     console.error('MongoDB connection error:', error);
//     process.exit(1);
//   }
// }

// // Function to check if file exists
// async function fileExists(filePath) {
//   try {
//     await fs.access(filePath);
//     return true;
//   } catch {
//     return false;
//   }
// }

// // Function to rename gallery images
// async function renameGalleryImages() {
//   console.log('\n=== Renaming Gallery Images ===');
  
//   const galleryItems = await Gallery.find({});
//   console.log(`Found ${galleryItems.length} gallery items`);
  
//   for (const item of galleryItems) {
//     try {
//       // Skip if already using slug-based filename
//       if (item.filename.startsWith(item.slug)) {
//         console.log(`✓ ${item.title}: Already using slug-based filename (${item.filename})`);
//         continue;
//       }
      
//       // Get file extension from current filename
//       const fileExtension = path.extname(item.filename);
//       const newFilename = `${item.slug}${fileExtension}`;
      
//       // Paths
//       const oldPath = path.join(__dirname, '../uploads', item.filename);
//       const newPath = path.join(__dirname, '../uploads', newFilename);
      
//       // Check if old file exists
//       if (!(await fileExists(oldPath))) {
//         console.log(`⚠ ${item.title}: Original file not found (${item.filename})`);
//         continue;
//       }
      
//       // Check if new filename already exists
//       if (await fileExists(newPath)) {
//         console.log(`⚠ ${item.title}: Target filename already exists (${newFilename})`);
//         continue;
//       }
      
//       // Rename the file
//       await fs.rename(oldPath, newPath);
      
//       // Update database
//       item.filename = newFilename;
//       item.imageUrl = `/uploads/${newFilename}`;
//       await item.save();
      
//       console.log(`✓ ${item.title}: ${item.filename} → ${newFilename}`);
      
//     } catch (error) {
//       console.error(`✗ Error processing ${item.title}:`, error.message);
//     }
//   }
// }

// // Function to rename food analysis images
// async function renameFoodAnalysisImages() {
//   console.log('\n=== Renaming Food Analysis Images ===');
  
//   const foodAnalyses = await FoodAnalysis.find({});
//   console.log(`Found ${foodAnalyses.length} food analysis items`);
  
//   for (const analysis of foodAnalyses) {
//     try {
//       // Extract current filename from imageUrl
//       const currentFilename = analysis.imageUrl.replace('/uploads/', '');
      
//       // Create new slug-based filename
//       const mealCategory = analysis.mealCategory || 'meal';
//       const timestamp = new Date(analysis.createdAt).getTime();
//       const fileExtension = path.extname(currentFilename);
//       const baseSlug = slugify(`${mealCategory}-${timestamp}`);
//       const newFilename = `${baseSlug}${fileExtension}`;
      
//       // Skip if already using similar naming
//       if (currentFilename.includes(mealCategory) && currentFilename.includes('-')) {
//         console.log(`✓ Food Analysis ${analysis._id}: Already using descriptive filename (${currentFilename})`);
//         continue;
//       }
      
//       // Paths
//       const oldPath = path.join(__dirname, '../uploads', currentFilename);
//       const newPath = path.join(__dirname, '../uploads', newFilename);
      
//       // Check if old file exists
//       if (!(await fileExists(oldPath))) {
//         console.log(`⚠ Food Analysis ${analysis._id}: Original file not found (${currentFilename})`);
//         continue;
//       }
      
//       // Check if new filename already exists
//       if (await fileExists(newPath)) {
//         // Add a counter to make it unique
//         let counter = 1;
//         let uniqueFilename = newFilename;
//         while (await fileExists(path.join(__dirname, '../uploads', uniqueFilename))) {
//           const nameWithoutExt = baseSlug + '-' + counter;
//           uniqueFilename = `${nameWithoutExt}${fileExtension}`;
//           counter++;
//         }
//         newFilename = uniqueFilename;
//         newPath = path.join(__dirname, '../uploads', newFilename);
//       }
      
//       // Rename the file
//       await fs.rename(oldPath, newPath);
      
//       // Update database
//       analysis.imageUrl = `/uploads/${newFilename}`;
//       await analysis.save();
      
//       console.log(`✓ Food Analysis ${analysis._id}: ${currentFilename} → ${newFilename}`);
      
//     } catch (error) {
//       console.error(`✗ Error processing Food Analysis ${analysis._id}:`, error.message);
//     }
//   }
// }

// // Main function
// async function main() {
//   console.log('🚀 Starting image renaming process...');
  
//   await connectDB();
  
//   try {
//     await renameGalleryImages();
//     await renameFoodAnalysisImages();
    
//     console.log('\n✅ Image renaming process completed!');
//     console.log('\nNext steps:');
//     console.log('1. Check the uploads folder to verify files were renamed correctly');
//     console.log('2. Test the gallery and food analysis pages to ensure images load properly');
//     console.log('3. Clear browser cache if needed');
    
//   } catch (error) {
//     console.error('❌ Error during renaming process:', error);
//   } finally {
//     await mongoose.disconnect();
//     console.log('Database connection closed');
//   }
// }

// // Run the script
// if (require.main === module) {
//   main().catch(console.error);
// }

// module.exports = { main };
