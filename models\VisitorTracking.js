const mongoose = require('mongoose');

const VisitorTrackingSchema = new mongoose.Schema({
  sessionId: {
    type: String,
    required: true,
    index: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    index: true
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: {
    type: String,
    required: true
  },
  browser: {
    name: String,
    version: String
  },
  os: {
    name: String,
    version: String
  },
  device: {
    type: String,
    enum: ['desktop', 'mobile', 'tablet', 'unknown'],
    default: 'unknown'
  },
  country: String,
  city: String,
  referrer: String,
  landingPage: String,
  currentPage: String,
  isOnline: {
    type: Boolean,
    default: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  sessionStart: {
    type: Date,
    default: Date.now
  },
  sessionEnd: Date,
  pageViews: {
    type: Number,
    default: 1
  },
  timeSpent: {
    type: Number, // in seconds
    default: 0
  },
  actions: [{
    type: {
      type: String,
      enum: ['page_view', 'click', 'form_submit', 'download', 'search', 'login', 'logout']
    },
    page: String,
    element: String,
    data: mongoose.Schema.Types.Mixed,
    timestamp: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  }
});

// Update last activity
VisitorTrackingSchema.methods.updateActivity = function(page = null) {
  this.lastActivity = new Date();
  this.pageViews += 1;
  
  if (page) {
    this.currentPage = page;
    this.actions.push({
      type: 'page_view',
      page: page,
      timestamp: new Date()
    });
  }
  
  return this.save();
};

// Mark session as ended
VisitorTrackingSchema.methods.endSession = function() {
  this.isOnline = false;
  this.sessionEnd = new Date();
  this.timeSpent = Math.floor((this.sessionEnd - this.sessionStart) / 1000);
  return this.save();
};

// Static method to get online users count
VisitorTrackingSchema.statics.getOnlineCount = async function() {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return await this.countDocuments({
    isOnline: true,
    lastActivity: { $gte: fiveMinutesAgo }
  });
};

// Static method to get daily visitor stats
VisitorTrackingSchema.statics.getDailyStats = async function(date = new Date()) {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  
  const stats = await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startOfDay, $lte: endOfDay }
      }
    },
    {
      $group: {
        _id: null,
        totalVisitors: { $sum: 1 },
        uniqueVisitors: { $addToSet: '$sessionId' },
        totalPageViews: { $sum: '$pageViews' },
        avgTimeSpent: { $avg: '$timeSpent' }
      }
    },
    {
      $project: {
        totalVisitors: 1,
        uniqueVisitors: { $size: '$uniqueVisitors' },
        totalPageViews: 1,
        avgTimeSpent: { $round: ['$avgTimeSpent', 2] }
      }
    }
  ]);
  
  return stats[0] || {
    totalVisitors: 0,
    uniqueVisitors: 0,
    totalPageViews: 0,
    avgTimeSpent: 0
  };
};

// Create indexes for better performance
VisitorTrackingSchema.index({ sessionId: 1 });
VisitorTrackingSchema.index({ userId: 1 });
VisitorTrackingSchema.index({ isOnline: 1, lastActivity: -1 });
VisitorTrackingSchema.index({ createdAt: -1 });

module.exports = mongoose.model('VisitorTracking', VisitorTrackingSchema);
