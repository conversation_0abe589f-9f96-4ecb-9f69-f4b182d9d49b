const mongoose = require('mongoose');

const GallerySchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  filename: {
    type: String,
    required: true
  },
  originalName: {
    type: String,
    required: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  slug: {
    type: String,
    unique: true,
    required: true
  },
  category: {
    type: String,
    enum: ['food', 'recipe', 'nutrition', 'general', 'banner'],
    default: 'general'
  },
  tags: {
    type: [String],
    default: []
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  cloudinaryPublicId: {
    type: String,
    required: false // For backward compatibility with existing records
  },
  altText: {
    type: String,
    trim: true
  },
  displayOrder: {
    type: Number,
    default: 0
  },
  metadata: {
    width: Number,
    height: Number,
    format: String
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
GallerySchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Create indexes for better performance
GallerySchema.index({ slug: 1 });
GallerySchema.index({ category: 1 });
GallerySchema.index({ isActive: 1 });
GallerySchema.index({ isFeatured: 1 });
GallerySchema.index({ createdAt: -1 });

module.exports = mongoose.model('Gallery', GallerySchema);
