/**
 * Simple test for save meal endpoint
 */

require('dotenv').config();
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: '123456'
};

// Mock AI analysis data (as would come from Gemini)
const mockAnalysisData = {
  food_items: [
    {
      name: "Hamburger",
      portion_size: "1 burger (approx. 200 grams)",
      calories: "600",
      protein: "25",
      carbohydrates: "45",
      fats: "35",
      confidence: "100",
      notes: "Assuming a typical cheeseburger with bun, beef patty, cheese, lettuce, and tomato."
    },
    {
      name: "French Fries",
      portion_size: "1 medium order (approx. 100 grams)",
      calories: "300",
      protein: "3",
      carbohydrates: "40",
      fats: "15",
      confidence: "100",
      notes: "Standard fast-food variety fries."
    }
  ],
  totals: {
    total_calories: "900",
    total_protein: "28",
    total_carbohydrates: "85",
    total_fats: "50"
  },
  overall_notes: "Nutritional data is approximate and based on standard fast-food portions."
};

async function testSaveMeal() {
  console.log('🧪 Testing Save Meal Endpoint...\n');
  
  try {
    // Step 1: Login to get JWT token
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, testUser);
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Test save meal endpoint
    console.log('\n2️⃣ Testing save meal to history...');
    
    const saveMealData = {
      mealCategory: 'lunch',
      mealDateTime: new Date().toISOString(),
      userNotes: 'Test meal from automated flow',
      analysisData: mockAnalysisData
    };

    console.log('📝 Sending data:');
    console.log('- Meal Category:', saveMealData.mealCategory);
    console.log('- Food Items:', mockAnalysisData.food_items.length);
    console.log('- Total Calories:', mockAnalysisData.totals.total_calories);

    const saveMealResponse = await axios.post(
      `${API_BASE}/food-analysis/save-meal`,
      saveMealData,
      { headers }
    );

    if (saveMealResponse.data.success) {
      console.log('✅ Meal saved successfully!');
      console.log(`📝 Meal ID: ${saveMealResponse.data.data.mealId}`);
      console.log(`🍽️ Category: ${saveMealResponse.data.data.mealCategory}`);
      console.log(`📊 Food items: ${saveMealResponse.data.data.foodItemsCount}`);
      console.log(`🔥 Total calories: ${saveMealResponse.data.data.nutritionalSummary.totalCalories}`);
      
      // Step 3: Verify meal appears in history
      console.log('\n3️⃣ Verifying meal in history...');
      
      const historyResponse = await axios.get(`${API_BASE}/food-analysis`, { headers });
      
      if (historyResponse.data.success) {
        const savedMeal = historyResponse.data.foodAnalyses.find(
          meal => meal._id === saveMealResponse.data.data.mealId
        );
        
        if (savedMeal) {
          console.log('✅ Meal found in history!');
          console.log(`🍽️ Category: ${savedMeal.mealCategory}`);
          console.log(`🥘 Food items: ${savedMeal.recognitionResults.length}`);
          console.log(`🔥 Calories: ${savedMeal.nutritionalSummary.calories}`);
          console.log(`🥩 Protein: ${savedMeal.nutritionalSummary.protein}g`);
          console.log(`🍞 Carbs: ${savedMeal.nutritionalSummary.carbs}g`);
          console.log(`🥑 Fats: ${savedMeal.nutritionalSummary.fat}g`);
        } else {
          console.log('⚠️ Meal not found in history');
        }
      }
      
      // Step 4: Test nutrition summary
      console.log('\n4️⃣ Testing nutrition summary...');
      
      const today = new Date().toISOString().split('T')[0];
      const summaryResponse = await axios.get(
        `${API_BASE}/food-analysis/nutrition-summary?period=daily&date=${today}`,
        { headers }
      );

      if (summaryResponse.data.success) {
        console.log('✅ Nutrition summary retrieved!');
        const summary = summaryResponse.data.data.summary;
        console.log(`📊 Today's totals:`);
        console.log(`   🍽️ Meals: ${summary.totalMeals}`);
        console.log(`   🔥 Calories: ${summary.totalCalories}`);
        console.log(`   🥩 Protein: ${summary.totalProtein}g`);
        console.log(`   🍞 Carbs: ${summary.totalCarbohydrates}g`);
        console.log(`   🥑 Fats: ${summary.totalFats}g`);
      }
      
      console.log('\n' + '='.repeat(60));
      console.log('🎉 Save Meal Flow Test: SUCCESS!');
      console.log('\n📋 Test Results:');
      console.log('✅ Login: Working');
      console.log('✅ Save meal: Working');
      console.log('✅ Meal history: Working');
      console.log('✅ Nutrition summary: Working');
      console.log('\n🚀 Backend is ready for frontend integration!');
      
    } else {
      throw new Error('Save meal failed: ' + saveMealResponse.data.message);
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('📝 Response status:', error.response.status);
      console.error('📝 Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend server is running on port 5000');
    console.log('2. Verify MongoDB is connected');
    console.log('3. Check that the test user exists in the database');
  }
}

// Run the test
testSaveMeal();
