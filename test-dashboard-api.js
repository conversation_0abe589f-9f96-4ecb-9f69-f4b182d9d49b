/**
 * Test script for the dashboard API endpoint
 */

require('dotenv').config();
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: '123456'
};

// Mock AI analysis data for creating test meals
const mockMeals = [
  {
    mealCategory: 'breakfast',
    mealDateTime: new Date().toISOString(),
    userNotes: 'Morning breakfast',
    analysisData: {
      food_items: [
        {
          name: "Oatmeal",
          portion_size: "1 cup (240g)",
          calories: "150",
          protein: "5",
          carbohydrates: "27",
          fats: "3",
          confidence: "100",
          notes: "Plain oatmeal with milk"
        },
        {
          name: "Banana",
          portion_size: "1 medium (118g)",
          calories: "105",
          protein: "1.3",
          carbohydrates: "27",
          fats: "0.4",
          confidence: "100",
          notes: "Fresh banana"
        }
      ],
      totals: {
        total_calories: "255",
        total_protein: "6.3",
        total_carbohydrates: "54",
        total_fats: "3.4"
      },
      overall_notes: "Healthy breakfast meal"
    }
  },
  {
    mealCategory: 'lunch',
    mealDateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    userNotes: 'Lunch meal',
    analysisData: {
      food_items: [
        {
          name: "Grilled Chicken Salad",
          portion_size: "1 large bowl (300g)",
          calories: "350",
          protein: "30",
          carbohydrates: "15",
          fats: "20",
          confidence: "95",
          notes: "Mixed greens with grilled chicken breast"
        }
      ],
      totals: {
        total_calories: "350",
        total_protein: "30",
        total_carbohydrates: "15",
        total_fats: "20"
      },
      overall_notes: "Healthy lunch salad"
    }
  }
];

async function testDashboardAPI() {
  console.log('🧪 Testing Dashboard API Endpoint...\n');
  
  try {
    // Step 1: Login to get JWT token
    console.log('1️⃣ Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, testUser);
    
    if (!loginResponse.data.success) {
      throw new Error('Login failed');
    }
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Create some test meals for dashboard data
    console.log('\n2️⃣ Creating test meals for dashboard...');
    
    for (let i = 0; i < mockMeals.length; i++) {
      const meal = mockMeals[i];
      console.log(`   Creating ${meal.mealCategory} meal...`);
      
      try {
        const saveMealResponse = await axios.post(
          `${API_BASE}/food-analysis/save-meal`,
          meal,
          { headers }
        );
        
        if (saveMealResponse.data.success) {
          console.log(`   ✅ ${meal.mealCategory} meal saved`);
        }
      } catch (error) {
        console.log(`   ⚠️ ${meal.mealCategory} meal save failed (might already exist)`);
      }
    }

    // Step 3: Test dashboard endpoint
    console.log('\n3️⃣ Testing dashboard endpoint...');
    
    const dashboardResponse = await axios.get(`${API_BASE}/food-analysis/dashboard`, { headers });

    if (dashboardResponse.data.success) {
      console.log('✅ Dashboard data retrieved successfully!\n');
      
      const data = dashboardResponse.data.data;
      
      // Display Today's Nutrition
      console.log('📊 TODAY\'S NUTRITION:');
      console.log(`   🔥 Calories: ${data.todaysNutrition.calories.current}/${data.todaysNutrition.calories.target} (${data.todaysNutrition.calories.percentage}%)`);
      console.log(`   🥩 Protein: ${data.todaysNutrition.protein.current}g/${data.todaysNutrition.protein.target}g (${data.todaysNutrition.protein.percentage}%)`);
      console.log(`   🍞 Carbs: ${data.todaysNutrition.carbs.current}g/${data.todaysNutrition.carbs.target}g (${data.todaysNutrition.carbs.percentage}%)`);
      console.log(`   🥑 Fat: ${data.todaysNutrition.fat.current}g/${data.todaysNutrition.fat.target}g (${data.todaysNutrition.fat.percentage}%)`);
      
      // Display Meal Breakdown
      console.log('\n🍽️ MEAL BREAKDOWN:');
      console.log(`   🌅 Breakfast: ${data.mealBreakdown.breakfast}`);
      console.log(`   🌞 Lunch: ${data.mealBreakdown.lunch}`);
      console.log(`   🌙 Dinner: ${data.mealBreakdown.dinner}`);
      console.log(`   🍿 Snacks: ${data.mealBreakdown.snacks}`);
      
      // Display Recent Meals
      console.log('\n📋 RECENT MEALS:');
      if (data.recentMeals.length > 0) {
        data.recentMeals.slice(0, 5).forEach((meal, index) => {
          const time = new Date(meal.dateTime).toLocaleTimeString('en-US', { 
            hour: '2-digit', 
            minute: '2-digit' 
          });
          console.log(`   ${index + 1}. ${meal.category} (${time}) - ${meal.calories} cal - ${meal.foodItemNames}`);
        });
      } else {
        console.log('   No recent meals found');
      }
      
      // Display Summary Stats
      console.log('\n📈 SUMMARY STATS:');
      console.log(`   🍽️ Meals today: ${data.summary.totalMealsToday}`);
      console.log(`   📅 Meals this week: ${data.summary.totalMealsThisWeek}`);
      console.log(`   🔥 Calories consumed today: ${data.summary.caloriesConsumedToday}`);
      console.log(`   ⏳ Calories remaining today: ${data.summary.caloriesRemainingToday}`);
      
      // Display Weekly Averages
      console.log('\n📊 WEEKLY AVERAGES:');
      console.log(`   🔥 Daily avg calories: ${data.weeklyAverages.calories}`);
      console.log(`   🥩 Daily avg protein: ${data.weeklyAverages.protein}g`);
      console.log(`   🍞 Daily avg carbs: ${data.weeklyAverages.carbs}g`);
      console.log(`   🥑 Daily avg fat: ${data.weeklyAverages.fat}g`);
      
      // Display Quick Actions
      console.log('\n⚡ QUICK ACTIONS:');
      console.log(`   📸 Can snap new: ${data.quickActions.canSnapNew ? 'Yes' : 'No'}`);
      console.log(`   📋 Has recent meals: ${data.quickActions.hasRecentMeals ? 'Yes' : 'No'}`);
      console.log(`   🎯 Needs nutrition goals: ${data.quickActions.needsNutritionGoals ? 'Yes' : 'No'}`);
      
      console.log('\n' + '='.repeat(60));
      console.log('🎉 Dashboard API Test: SUCCESS!');
      console.log('\n📋 API Response Structure:');
      console.log('✅ todaysNutrition: Complete with goals and progress');
      console.log('✅ mealBreakdown: Categorized meal counts');
      console.log('✅ recentMeals: Formatted meal list');
      console.log('✅ weeklyAverages: Daily averages for comparison');
      console.log('✅ summary: Key statistics');
      console.log('✅ quickActions: UI state indicators');
      console.log('\n🚀 Dashboard endpoint is ready for frontend integration!');
      
    } else {
      throw new Error('Dashboard API failed: ' + dashboardResponse.data.message);
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    
    if (error.response) {
      console.error('📝 Response status:', error.response.status);
      console.error('📝 Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure the backend server is running on port 5000');
    console.log('2. Verify MongoDB is connected');
    console.log('3. Check that the test user exists in the database');
  }
}

// Run the test
testDashboardAPI();
