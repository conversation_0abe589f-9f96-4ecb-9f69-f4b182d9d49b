const mongoose = require('mongoose');

const FoodItemSchema = new mongoose.Schema({
  foodItem: {
    type: String,
    required: true
  },
  confidence: {
    type: Number,
    required: true
  },
  boundingBox: {
    x: Number,
    y: Number,
    width: Number,
    height: Number
  },
  quantityGrams: {
    type: Number,
    required: true
  },
  commonPortions: {
    type: [String],
    default: []
  },
  selectedPortion: {
    type: String,
    required: true
  },
  userVerified: {
    type: Boolean,
    default: false
  },
  userAdded: {
    type: Boolean,
    default: false
  }
});

const NutritionalSummarySchema = new mongoose.Schema({
  calories: {
    type: Number,
    required: true
  },
  protein: {
    type: Number,
    required: true
  },
  carbs: {
    type: Number,
    required: true
  },
  fat: {
    type: Number,
    required: true
  },
  fiber: {
    type: Number,
    required: true
  },
  sugar: {
    type: Number,
    required: true
  },
  sodium: {
    type: Number,
    required: true
  }
});

const FoodAnalysisSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  imageUrl: {
    type: String,
    required: true
  },
  mealCategory: {
    type: String,
    required: true,
    enum: ['breakfast', 'lunch', 'dinner', 'snack', 'dessert', 'other']
  },
  mealDateTime: {
    type: Date,
    required: true,
    default: Date.now
  },
  userNotes: {
    type: String,
    default: ''
  },
  recognitionResults: [FoodItemSchema],
  nutritionalSummary: NutritionalSummarySchema,
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model('FoodAnalysis', FoodAnalysisSchema);
