# 📊 Status & Trends Frontend Implementation Guide

## 🎯 Overview

The `/status-trends` page provides users with comprehensive insights into their nutrition patterns, goal progress, and health trends. This guide shows how to implement the frontend components.

## 📡 API Endpoint

**GET** `/api/status-trends?period=30`

**Query Parameters:**
- `period`: Number of days (7, 30, or 90)

## 🎨 Frontend Component Structure

### **1. Main Status & Trends Component**

```typescript
// StatusTrends.tsx
import React, { useState, useEffect } from 'react';
import { formatDisplayNumber } from '../utils/numberUtils';
import TrendChart from './components/TrendChart';
import GoalProgress from './components/GoalProgress';
import MealPatterns from './components/MealPatterns';
import Achievements from './components/Achievements';
import HealthInsights from './components/HealthInsights';

interface StatusTrendsData {
  period: number;
  dateRange: { start: string; end: string };
  nutritionGoals: any;
  dailyTrends: any[];
  weeklyAverages: any;
  mealPatterns: any;
  goalProgress: any;
  achievements: any;
  insights: any[];
  trendComparisons: any;
  summary: any;
}

const StatusTrends: React.FC = () => {
  const [data, setData] = useState<StatusTrendsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [period, setPeriod] = useState(30);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStatusTrends();
  }, [period]);

  const fetchStatusTrends = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const response = await fetch(`/api/status-trends?period=${period}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch status trends');
      }

      const result = await response.json();
      
      if (result.success) {
        setData(result.data);
        setError(null);
      } else {
        setError(result.message || 'Failed to load data');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="status-trends-loading">
        <div className="loading-spinner"></div>
        <p>Loading your trends...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="status-trends-error">
        <h3>Unable to load trends</h3>
        <p>{error}</p>
        <button onClick={fetchStatusTrends} className="retry-button">
          Try Again
        </button>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="status-trends-empty">
        <h3>No data available</h3>
        <p>Start logging meals to see your trends!</p>
      </div>
    );
  }

  return (
    <div className="status-trends">
      <div className="status-trends-header">
        <h1>📊 Status & Trends</h1>
        <div className="period-selector">
          <button 
            className={period === 7 ? 'active' : ''}
            onClick={() => setPeriod(7)}
          >
            7 Days
          </button>
          <button 
            className={period === 30 ? 'active' : ''}
            onClick={() => setPeriod(30)}
          >
            30 Days
          </button>
          <button 
            className={period === 90 ? 'active' : ''}
            onClick={() => setPeriod(90)}
          >
            90 Days
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="summary-cards">
        <div className="summary-card">
          <h3>Total Meals</h3>
          <div className="value">{data.summary.totalMeals}</div>
          <div className="subtitle">
            {formatDisplayNumber(data.summary.avgMealsPerDay)} per day
          </div>
        </div>
        
        <div className="summary-card">
          <h3>Active Days</h3>
          <div className="value">{data.summary.activeDays}</div>
          <div className="subtitle">
            out of {data.period} days
          </div>
        </div>
        
        <div className="summary-card">
          <h3>Consistency</h3>
          <div className="value">{data.summary.consistencyScore}%</div>
          <div className="subtitle">tracking score</div>
        </div>
        
        <div className="summary-card">
          <h3>Current Streak</h3>
          <div className="value">{data.achievements.currentStreak}</div>
          <div className="subtitle">days in a row</div>
        </div>
      </div>

      {/* Health Insights */}
      <HealthInsights insights={data.insights} />

      {/* Goal Progress */}
      <GoalProgress 
        goalProgress={data.goalProgress}
        nutritionGoals={data.nutritionGoals}
        weeklyAverages={data.weeklyAverages}
      />

      {/* Nutrition Trends Chart */}
      <TrendChart 
        dailyTrends={data.dailyTrends}
        trendComparisons={data.trendComparisons}
        period={data.period}
      />

      {/* Meal Patterns */}
      <MealPatterns patterns={data.mealPatterns} />

      {/* Achievements */}
      <Achievements achievements={data.achievements} />
    </div>
  );
};

export default StatusTrends;
```

### **2. Health Insights Component**

```typescript
// components/HealthInsights.tsx
import React from 'react';

interface Insight {
  type: 'success' | 'warning' | 'info' | 'tip';
  title: string;
  message: string;
}

interface HealthInsightsProps {
  insights: Insight[];
}

const HealthInsights: React.FC<HealthInsightsProps> = ({ insights }) => {
  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'info': return 'ℹ️';
      case 'tip': return '💡';
      default: return 'ℹ️';
    }
  };

  const getInsightClass = (type: string) => {
    return `insight-card insight-${type}`;
  };

  return (
    <div className="health-insights">
      <h2>🔍 Health Insights</h2>
      <div className="insights-grid">
        {insights.map((insight, index) => (
          <div key={index} className={getInsightClass(insight.type)}>
            <div className="insight-header">
              <span className="insight-icon">{getInsightIcon(insight.type)}</span>
              <h3>{insight.title}</h3>
            </div>
            <p>{insight.message}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HealthInsights;
```

### **3. Goal Progress Component**

```typescript
// components/GoalProgress.tsx
import React from 'react';
import { formatDisplayNumber } from '../../utils/numberUtils';

interface GoalProgressProps {
  goalProgress: any;
  nutritionGoals: any;
  weeklyAverages: any;
}

const GoalProgress: React.FC<GoalProgressProps> = ({ 
  goalProgress, 
  nutritionGoals, 
  weeklyAverages 
}) => {
  const nutrients = ['calories', 'protein', 'carbs', 'fat'];

  return (
    <div className="goal-progress">
      <h2>🎯 Goal Progress</h2>
      <div className="progress-grid">
        {nutrients.map(nutrient => {
          const progress = goalProgress[nutrient];
          const goal = nutritionGoals[nutrient];
          const average = weeklyAverages[nutrient];
          const unit = nutrient === 'calories' ? 'kcal' : 'g';
          
          return (
            <div key={nutrient} className="progress-card">
              <div className="progress-header">
                <h3>{nutrient.charAt(0).toUpperCase() + nutrient.slice(1)}</h3>
                <span className="progress-percentage">
                  {progress.percentage}% on target
                </span>
              </div>
              
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${Math.min(progress.percentage, 100)}%` }}
                ></div>
              </div>
              
              <div className="progress-details">
                <div className="detail">
                  <span className="label">Average:</span>
                  <span className="value">
                    {formatDisplayNumber(average, nutrient === 'calories' ? 0 : 1)}{unit}
                  </span>
                </div>
                <div className="detail">
                  <span className="label">Goal:</span>
                  <span className="value">{goal}{unit}</span>
                </div>
                <div className="detail">
                  <span className="label">Days achieved:</span>
                  <span className="value">{progress.achieved}</span>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default GoalProgress;
```

### **4. Trend Chart Component**

```typescript
// components/TrendChart.tsx
import React, { useState } from 'react';
import { formatDisplayNumber } from '../../utils/numberUtils';

interface TrendChartProps {
  dailyTrends: any[];
  trendComparisons: any;
  period: number;
}

const TrendChart: React.FC<TrendChartProps> = ({ 
  dailyTrends, 
  trendComparisons, 
  period 
}) => {
  const [selectedMetric, setSelectedMetric] = useState('calories');
  
  const metrics = [
    { key: 'calories', label: 'Calories', unit: 'kcal', color: '#ff6b6b' },
    { key: 'protein', label: 'Protein', unit: 'g', color: '#4ecdc4' },
    { key: 'carbs', label: 'Carbs', unit: 'g', color: '#45b7d1' },
    { key: 'fat', label: 'Fat', unit: 'g', color: '#f9ca24' }
  ];

  const selectedMetricData = metrics.find(m => m.key === selectedMetric);
  
  // Calculate chart dimensions and data points
  const chartWidth = 800;
  const chartHeight = 300;
  const padding = 40;
  
  const maxValue = Math.max(...dailyTrends.map(day => day[selectedMetric]));
  const minValue = Math.min(...dailyTrends.map(day => day[selectedMetric]));
  const valueRange = maxValue - minValue || 1;
  
  const points = dailyTrends.map((day, index) => {
    const x = padding + (index / (dailyTrends.length - 1)) * (chartWidth - 2 * padding);
    const y = chartHeight - padding - ((day[selectedMetric] - minValue) / valueRange) * (chartHeight - 2 * padding);
    return { x, y, value: day[selectedMetric], date: day.date };
  });

  const pathData = points.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ');

  return (
    <div className="trend-chart">
      <h2>📈 Nutrition Trends</h2>
      
      {/* Metric Selector */}
      <div className="metric-selector">
        {metrics.map(metric => (
          <button
            key={metric.key}
            className={selectedMetric === metric.key ? 'active' : ''}
            onClick={() => setSelectedMetric(metric.key)}
            style={{ borderColor: metric.color }}
          >
            {metric.label}
          </button>
        ))}
      </div>

      {/* Trend Comparison */}
      {trendComparisons && (
        <div className="trend-comparison">
          <div className="comparison-item">
            <span className="label">{selectedMetricData?.label} Change:</span>
            <span className={`value ${trendComparisons[selectedMetric]?.change >= 0 ? 'positive' : 'negative'}`}>
              {trendComparisons[selectedMetric]?.change >= 0 ? '+' : ''}
              {formatDisplayNumber(trendComparisons[selectedMetric]?.change)}
              {selectedMetricData?.unit}
              ({trendComparisons[selectedMetric]?.percentage >= 0 ? '+' : ''}
              {trendComparisons[selectedMetric]?.percentage}%)
            </span>
          </div>
        </div>
      )}

      {/* Chart */}
      <div className="chart-container">
        <svg width={chartWidth} height={chartHeight} className="trend-svg">
          {/* Grid lines */}
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#f0f0f0" strokeWidth="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          {/* Trend line */}
          <path
            d={pathData}
            fill="none"
            stroke={selectedMetricData?.color}
            strokeWidth="3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          
          {/* Data points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={selectedMetricData?.color}
              className="data-point"
            >
              <title>
                {point.date}: {formatDisplayNumber(point.value)} {selectedMetricData?.unit}
              </title>
            </circle>
          ))}
          
          {/* Y-axis labels */}
          <text x="10" y={padding} textAnchor="start" className="axis-label">
            {formatDisplayNumber(maxValue)} {selectedMetricData?.unit}
          </text>
          <text x="10" y={chartHeight - padding} textAnchor="start" className="axis-label">
            {formatDisplayNumber(minValue)} {selectedMetricData?.unit}
          </text>
        </svg>
      </div>
    </div>
  );
};

export default TrendChart;

### **5. Meal Patterns Component**

```typescript
// components/MealPatterns.tsx
import React from 'react';

interface MealPatternsProps {
  patterns: {
    byCategory: Record<string, number>;
    byTimeOfDay: Record<string, number>;
  };
}

const MealPatterns: React.FC<MealPatternsProps> = ({ patterns }) => {
  const categoryData = Object.entries(patterns.byCategory)
    .filter(([_, count]) => count > 0)
    .sort(([_, a], [__, b]) => b - a);

  const timeData = Object.entries(patterns.byTimeOfDay)
    .filter(([_, count]) => count > 0)
    .sort(([_, a], [__, b]) => b - a);

  const totalMeals = Object.values(patterns.byCategory).reduce((sum, count) => sum + count, 0);

  const getCategoryIcon = (category: string) => {
    const icons: Record<string, string> = {
      breakfast: '🌅',
      lunch: '☀️',
      dinner: '🌙',
      snack: '🍎',
      dessert: '🍰',
      other: '🍽️'
    };
    return icons[category] || '🍽️';
  };

  const getTimeIcon = (time: string) => {
    const icons: Record<string, string> = {
      morning: '🌅',
      afternoon: '☀️',
      evening: '🌆',
      night: '🌙'
    };
    return icons[time] || '🕐';
  };

  return (
    <div className="meal-patterns">
      <h2>🍽️ Meal Patterns</h2>

      <div className="patterns-grid">
        {/* Meal Categories */}
        <div className="pattern-section">
          <h3>By Meal Type</h3>
          <div className="pattern-items">
            {categoryData.map(([category, count]) => {
              const percentage = Math.round((count / totalMeals) * 100);
              return (
                <div key={category} className="pattern-item">
                  <div className="pattern-header">
                    <span className="pattern-icon">{getCategoryIcon(category)}</span>
                    <span className="pattern-name">
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </span>
                    <span className="pattern-percentage">{percentage}%</span>
                  </div>
                  <div className="pattern-bar">
                    <div
                      className="pattern-fill"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="pattern-count">{count} meals</div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Time of Day */}
        <div className="pattern-section">
          <h3>By Time of Day</h3>
          <div className="pattern-items">
            {timeData.map(([time, count]) => {
              const percentage = Math.round((count / totalMeals) * 100);
              return (
                <div key={time} className="pattern-item">
                  <div className="pattern-header">
                    <span className="pattern-icon">{getTimeIcon(time)}</span>
                    <span className="pattern-name">
                      {time.charAt(0).toUpperCase() + time.slice(1)}
                    </span>
                    <span className="pattern-percentage">{percentage}%</span>
                  </div>
                  <div className="pattern-bar">
                    <div
                      className="pattern-fill"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <div className="pattern-count">{count} meals</div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MealPatterns;

### **6. Achievements Component**

```typescript
// components/Achievements.tsx
import React from 'react';

interface Achievement {
  currentStreak: number;
  longestStreak: number;
  totalActiveDays: number;
  perfectDays: number;
  badges: Array<{
    name: string;
    description: string;
  }>;
}

interface AchievementsProps {
  achievements: Achievement;
}

const Achievements: React.FC<AchievementsProps> = ({ achievements }) => {
  const getStreakMessage = (streak: number) => {
    if (streak === 0) return "Start your journey today!";
    if (streak === 1) return "Great start! Keep it up!";
    if (streak < 7) return "Building momentum!";
    if (streak < 30) return "You're on fire! 🔥";
    return "Incredible dedication! 🌟";
  };

  const getStreakColor = (streak: number) => {
    if (streak === 0) return "#ccc";
    if (streak < 7) return "#ffc107";
    if (streak < 30) return "#fd7e14";
    return "#28a745";
  };

  return (
    <div className="achievements">
      <h2>🏆 Achievements</h2>

      <div className="achievements-grid">
        {/* Current Streak */}
        <div className="achievement-card streak-card">
          <div className="achievement-icon">🔥</div>
          <h3>Current Streak</h3>
          <div
            className="achievement-value"
            style={{ color: getStreakColor(achievements.currentStreak) }}
          >
            {achievements.currentStreak}
          </div>
          <div className="achievement-unit">days</div>
          <div className="achievement-message">
            {getStreakMessage(achievements.currentStreak)}
          </div>
        </div>

        {/* Longest Streak */}
        <div className="achievement-card">
          <div className="achievement-icon">📈</div>
          <h3>Best Streak</h3>
          <div className="achievement-value">{achievements.longestStreak}</div>
          <div className="achievement-unit">days</div>
          <div className="achievement-message">Personal record!</div>
        </div>

        {/* Total Active Days */}
        <div className="achievement-card">
          <div className="achievement-icon">📅</div>
          <h3>Active Days</h3>
          <div className="achievement-value">{achievements.totalActiveDays}</div>
          <div className="achievement-unit">total</div>
          <div className="achievement-message">Days with meals logged</div>
        </div>

        {/* Perfect Days */}
        <div className="achievement-card">
          <div className="achievement-icon">⭐</div>
          <h3>Perfect Days</h3>
          <div className="achievement-value">{achievements.perfectDays}</div>
          <div className="achievement-unit">days</div>
          <div className="achievement-message">Goals met perfectly!</div>
        </div>
      </div>

      {/* Badges */}
      {achievements.badges.length > 0 && (
        <div className="badges-section">
          <h3>🎖️ Earned Badges</h3>
          <div className="badges-grid">
            {achievements.badges.map((badge, index) => (
              <div key={index} className="badge-item">
                <div className="badge-icon">🏅</div>
                <div className="badge-content">
                  <h4>{badge.name}</h4>
                  <p>{badge.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Motivational Section */}
      <div className="motivation-section">
        <h3>🎯 Keep Going!</h3>
        <div className="motivation-tips">
          {achievements.currentStreak === 0 && (
            <p>💡 Start by logging just one meal today to begin your streak!</p>
          )}
          {achievements.currentStreak > 0 && achievements.currentStreak < 7 && (
            <p>💡 You're doing great! Try to reach a 7-day streak for your first badge!</p>
          )}
          {achievements.currentStreak >= 7 && achievements.currentStreak < 30 && (
            <p>💡 Amazing consistency! Can you make it to 30 days?</p>
          )}
          {achievements.currentStreak >= 30 && (
            <p>💡 You're a nutrition tracking champion! Keep up the excellent work!</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Achievements;
```

## 🎨 CSS Styles

```css
/* StatusTrends.css */
.status-trends {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.status-trends-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.period-selector {
  display: flex;
  gap: 10px;
}

.period-selector button {
  padding: 8px 16px;
  border: 2px solid #ddd;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.period-selector button.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
}

.summary-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
}

.summary-card .value {
  font-size: 32px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.summary-card .subtitle {
  color: #888;
  font-size: 12px;
}

.health-insights {
  margin-bottom: 30px;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 15px;
}

.insight-card {
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.insight-success {
  background: #f0f9f0;
  border-left-color: #28a745;
}

.insight-warning {
  background: #fff8e1;
  border-left-color: #ffc107;
}

.insight-info {
  background: #e3f2fd;
  border-left-color: #17a2b8;
}

.insight-tip {
  background: #f3e5f5;
  border-left-color: #6f42c1;
}

.insight-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.insight-icon {
  font-size: 18px;
}

.goal-progress {
  margin-bottom: 30px;
}

.progress-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.progress-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.progress-percentage {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4ecdc4, #44a08d);
  transition: width 0.3s ease;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.detail .label {
  color: #666;
}

.detail .value {
  font-weight: 500;
  color: #333;
}

.trend-chart {
  margin-bottom: 30px;
}

.metric-selector {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.metric-selector button {
  padding: 8px 16px;
  border: 2px solid;
  background: white;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.metric-selector button.active {
  background: currentColor;
  color: white;
}

.chart-container {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow-x: auto;
}

.trend-svg {
  width: 100%;
  height: auto;
}

.data-point {
  cursor: pointer;
  transition: r 0.2s ease;
}

.data-point:hover {
  r: 6;
}

.axis-label {
  font-size: 12px;
  fill: #666;
}

.trend-comparison {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.comparison-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comparison-item .value.positive {
  color: #28a745;
}

.comparison-item .value.negative {
  color: #dc3545;
}

/* Loading and Error States */
.status-trends-loading,
.status-trends-error,
.status-trends-empty {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
}

.retry-button:hover {
  background: #0056b3;
}

/* Meal Patterns Styles */
.meal-patterns {
  margin-bottom: 30px;
}

.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.pattern-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.pattern-section h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
}

.pattern-items {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.pattern-item {
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.pattern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.pattern-icon {
  font-size: 18px;
  margin-right: 8px;
}

.pattern-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.pattern-percentage {
  font-weight: bold;
  color: #007bff;
}

.pattern-bar {
  width: 100%;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 5px;
}

.pattern-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.pattern-count {
  font-size: 12px;
  color: #666;
  text-align: right;
}

/* Achievements Styles */
.achievements {
  margin-bottom: 30px;
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.achievement-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
  transition: transform 0.2s ease;
}

.achievement-card:hover {
  transform: translateY(-2px);
}

.achievement-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.achievement-card h3 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.achievement-value {
  font-size: 36px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.achievement-unit {
  color: #888;
  font-size: 12px;
  margin-bottom: 10px;
}

.achievement-message {
  color: #666;
  font-size: 13px;
  font-style: italic;
}

.streak-card .achievement-value {
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.badges-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.badges-section h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.badges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.badge-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.badge-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.badge-content h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.badge-content p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

.motivation-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
}

.motivation-section h3 {
  margin: 0 0 15px 0;
  color: white;
}

.motivation-tips p {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-trends-header {
    flex-direction: column;
    gap: 20px;
  }
  
  .summary-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .progress-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
}
```

## 🚀 Implementation Steps

1. **Create the route**: ✅ Already implemented
2. **Add to navigation**: Add link to `/status-trends` in your main navigation
3. **Create components**: Implement the React components above
4. **Add styling**: Apply the CSS styles
5. **Test functionality**: Verify data loading and chart rendering

## 📊 Key Features

- **📈 Interactive Charts**: Visual nutrition trends over time
- **🎯 Goal Tracking**: Progress towards nutrition goals
- **🏆 Achievements**: Streaks and badges for motivation
- **💡 Smart Insights**: Personalized recommendations
- **📱 Responsive Design**: Works on all devices
- **⚡ Real-time Data**: Updates based on meal logging

This implementation provides a comprehensive view of user progress and helps motivate continued engagement with the food analysis app!
