const express = require('express');
const router = express.Router();
const { protect, admin } = require('../middleware/auth');
const Announcement = require('../models/Announcement');

// @route   GET /api/announcements
// @desc    Get all active announcements with filtering and pagination
// @access  Public
router.get('/', async (req, res) => {
  try {
    const currentDate = new Date();
    const { page = 1, limit = 10, category, importance } = req.query;

    // Build filter query
    const filter = {
      publishDate: { $lte: currentDate },
      $or: [
        { expiryDate: { $gte: currentDate } }, //  expired
        { expiryDate: null }                   // No expiry date (never expires)
      ]
    };



    // Add cateNotgory filter if provided
    if (category && category !== 'all') {
      filter.category = category;
    }

    // Add importance filter if provided
    if (importance && importance !== 'all') {
      filter.importance = importance;
    }

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get total count for pagination
    const total = await Announcement.countDocuments(filter);
    const totalPages = Math.ceil(total / limitNum);

    // Find active announcements with filters and pagination
    const announcements = await Announcement.find(filter)
      .sort({ importance: -1, publishDate: -1 })
      .skip(skip)
      .limit(limitNum);



    res.json({
      success: true,
      count: announcements.length,
      announcements,
      pagination: {
        current: pageNum,
        limit: limitNum,
        total: total,
        pages: totalPages
      }
    });
  } catch (error) {
    console.error('Error in get announcements:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/announcements/all
// @desc    Get all announcements (including expired)
// @access  Private (Admin only)
router.get('/all', [protect, admin], async (req, res) => {
  try {
    // Find all announcements
    const announcements = await Announcement.find().sort({ publishDate: -1 });
    
    res.json({
      success: true,
      count: announcements.length,
      announcements
    });
  } catch (error) {
    console.error('Error in get all announcements:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   GET /api/announcements/:id
// @desc    Get announcement by ID
// @access  Private (Admin only)
router.get('/:id', [protect, admin], async (req, res) => {
  try {
    // Find announcement by ID
    const announcement = await Announcement.findById(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    res.json({
      success: true,
      announcement
    });
  } catch (error) {
    console.error('Error in get announcement by ID:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   POST /api/announcements
// @desc    Create a new announcement
// @access  Private (Admin only)
router.post('/', [protect, admin], async (req, res) => {
  try {
    const { title, content, category, importance, publishDate, expiryDate } = req.body;
    
    // Create new announcement
    const announcement = new Announcement({
      title,
      content,
      category,
      importance,
      publishDate: publishDate || new Date(),
      expiryDate
    });
    
    // Save announcement to database
    await announcement.save();
    
    res.status(201).json({
      success: true,
      announcement
    });
  } catch (error) {
    console.error('Error in create announcement:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   PUT /api/announcements/:id
// @desc    Update an announcement
// @access  Private (Admin only)
router.put('/:id', [protect, admin], async (req, res) => {
  try {
    const { title, content, category, importance, publishDate, expiryDate } = req.body;
    
    // Find announcement by ID
    let announcement = await Announcement.findById(req.params.id);
    
    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }
    
    // Update fields
    if (title) announcement.title = title;
    if (content) announcement.content = content;
    if (category) announcement.category = category;
    if (importance) announcement.importance = importance;
    if (publishDate) announcement.publishDate = publishDate;
    if (expiryDate) announcement.expiryDate = expiryDate;
    
    // Save updated announcement
    await announcement.save();
    
    res.json({
      success: true,
      announcement
    });
  } catch (error) {
    console.error('Error in update announcement:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// @route   DELETE /api/announcements/:id
// @desc    Delete an announcement
// @access  Private (Admin only)
router.delete('/:id', [protect, admin], async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Attempting to delete announcement with ID:', id);

    // Validate ObjectId format
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid announcement ID format'
      });
    }

    // Find and delete announcement by ID
    const announcement = await Announcement.findByIdAndDelete(id);

    if (!announcement) {
      return res.status(404).json({
        success: false,
        message: 'Announcement not found'
      });
    }

    console.log('Successfully deleted announcement:', announcement._id);

    res.json({
      success: true,
      message: 'Announcement deleted'
    });
  } catch (error) {
    console.error('Error in delete announcement:', error);
    console.error('Error details:', error.message);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
